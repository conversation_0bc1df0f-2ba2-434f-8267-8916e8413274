import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import i18n from './locales/i18n'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(i18n)

app.mount('#app')

import './assets/main.css'
// 导入导航对齐修复CSS
import './assets/navigation-fix.css'

// 全局导入必要的CSS文件，确保所有页面都能正确显示
// 这些CSS文件对于保持网站的一致性和正确的样式显示至关重要
const loadGlobalCSS = () => {
  // Bootstrap CSS
  const bootstrapLink = document.createElement('link')
  bootstrapLink.rel = 'stylesheet'
  bootstrapLink.href = '/assets/vendor/bootstrap/css/bootstrap.min.css'
  bootstrapLink.id = 'global-bootstrap-css'
  if (!document.getElementById('global-bootstrap-css')) {
    document.head.appendChild(bootstrapLink)
  }

  // FontAwesome CSS
  const fontAwesomeLink = document.createElement('link')
  fontAwesomeLink.rel = 'stylesheet'
  fontAwesomeLink.href = '/assets/vendor/fontawesome/css/font-awesome.css'
  fontAwesomeLink.id = 'global-fontawesome-css'
  if (!document.getElementById('global-fontawesome-css')) {
    document.head.appendChild(fontAwesomeLink)
  }

  // Main Style CSS
  const styleLink = document.createElement('link')
  styleLink.rel = 'stylesheet'
  styleLink.href = '/assets/custom/css/style.css'
  styleLink.id = 'global-style-css'
  if (!document.getElementById('global-style-css')) {
    document.head.appendChild(styleLink)
  }
}

// 在应用启动时加载全局CSS
loadGlobalCSS()
