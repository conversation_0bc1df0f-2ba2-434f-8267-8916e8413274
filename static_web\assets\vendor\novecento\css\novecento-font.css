/**
 * @license
 * MyFonts Webfont Build ID 2886126, 2014-09-18T22:39:09-0400
 * 
 * The fonts listed in this notice are subject to the End User License
 * Agreement(s) entered into by the website owner. All other parties are 
 * explicitly restricted from using the Licensed Webfonts(s).
 * 
 * You may obtain a valid license at the URLs below.
 * 
 * Webfont: Novecentosanswide-Bold by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-bold/
 * 
 * Webfont: Novecentosanswide-Book by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-book/
 * 
 * Webfont: Novecentosanswide-DemiBold by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-demibold/
 * 
 * Webfont: Novecentosanswide-Light by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-light/
 * 
 * Webfont: Novecentosanswide-Medium by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-medium/
 * 
 * Webfont: Novecentosanswide-Normal by Synthview
 * URL: http://www.myfonts.com/fonts/synthview/novecento/wide-normal/
 * 
 * 
 * License: http://www.myfonts.com/viewlicense?type=web&buildid=2886126
 * Licensed pageviews: 10,000
 * Webfonts copyright: Copyright &#x00A9; 2013 by Jan Tonellato. All rights reserved.
 * 
 * © 2014 MyFonts Inc
*/


/* @import must be at top of file, otherwise CSS will not work */
@import url("//hello.myfonts.net/count/2c09ee");

  
@font-face {font-family: 'Novecentosanswide-Bold';src: url('../fonts/2C09EE_0_0.eot');src: url('../fonts/2C09EE_0_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_0_0.woff') format('woff'),url('../fonts/2C09EE_0_0.ttf') format('truetype');}
 
  
@font-face {font-family: 'Novecentosanswide-Book';src: url('../fonts/2C09EE_1_0.eot');src: url('../fonts/2C09EE_1_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_1_0.woff') format('woff'),url('../fonts/2C09EE_1_0.ttf') format('truetype');}
 
  
@font-face {font-family: 'Novecentosanswide-DemiBold';src: url('../fonts/2C09EE_2_0.eot');src: url('../fonts/2C09EE_2_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_2_0.woff') format('woff'),url('../fonts/2C09EE_2_0.ttf') format('truetype');}
 
  
@font-face {font-family: 'Novecentosanswide-Light';src: url('../fonts/2C09EE_3_0.eot');src: url('../fonts/2C09EE_3_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_3_0.woff') format('woff'),url('../fonts/2C09EE_3_0.ttf') format('truetype');}
 
  
@font-face {font-family: 'Novecentosanswide-Medium';src: url('../fonts/2C09EE_4_0.eot');src: url('../fonts/2C09EE_4_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_4_0.woff') format('woff'),url('../fonts/2C09EE_4_0.ttf') format('truetype');}
 
  
@font-face {font-family: 'Novecentosanswide-Normal';src: url('../fonts/2C09EE_5_0.eot');src: url('../fonts/2C09EE_5_0.eot?#iefix') format('embedded-opentype'),url('../fonts/2C09EE_5_0.woff') format('woff'),url('../fonts/2C09EE_5_0.ttf') format('truetype');}
 