<!DOCTYPE html>
<html lang="en">
<!--[if IE 9]>  <html class="ie9" lang="en">    <![endif]-->
<!--[if IE 8]>  <html class="ie8" lang="en">    <![endif]-->
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Electronic Responsive HTML Template | UI Elements </title>

     <meta name="description" content="Electronic Responsive HTML Template is the professional responsive multi-purpose website template, based on the latest Bootstrap 3. It covers all possible web site development cases">
     <meta name="keywords" content="html5, template, css3, creative, portfolio, website, blog, personal, corporative, design, e-commerce, shop, store, design, modern, responsive, bootstrap, slider, masonry ">

    <!-- CSS -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css"                     rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/fontawesome/css/font-awesome.css"                    rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/novecento/css/novecento-font.css"                    rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/fancybox/css/jquery.fancybox.css?v=2.1.5"            rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/fancybox/css/jquery.fancybox-buttons.css?v=1.0.5"    rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/flexslider/css/flexslider.css"                       rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/vendor/feather/css/feather.css"                             rel="stylesheet" type="text/css" media="screen" />
    <link href="assets/custom/css/style.css"                                       rel="stylesheet" type="text/css" media="screen" />

     <!-- JS -->
     <script src="assets/vendor/jquery/js/jquery-1.10.1.min.js"                     type="text/javascript"></script>
     <script src="assets/vendor/bootstrap/js/bootstrap.min.js"                      type="text/javascript"></script>
     <script src="assets/vendor/sticky/js/jquery.sticky.js"                         type="text/javascript"></script>
     <script src="assets/vendor/mousewheel/js/jquery.mousewheel-3.0.6.pack.js"      type="text/javascript"></script>
     <script src="assets/vendor/fancybox/js/jquery.fancybox.pack.js?v=2.1.5"        type="text/javascript"></script>
     <script src="assets/vendor/fancybox/js/jquery.fancybox-buttons.js?v=1.0.5"     type="text/javascript"></script>
     <script src="assets/vendor/fancybox/js/jquery.fancybox-media.js?v=1.0.6"       type="text/javascript"></script>
     <script src="assets/vendor/superfish/js/hoverIntent.js"                        type="text/javascript"></script>
     <script src="assets/vendor/superfish/js/superfish.js"                          type="text/javascript"></script>
     <script src="assets/vendor/superfish/js/supersubs.js"                          type="text/javascript"></script>
     <script src="assets/vendor/flickr/js/jflickrfeed.min.js"                       type="text/javascript"></script>
     <script src="assets/vendor/flexslider/js/jquery.flexslider-min.js"             type="text/javascript"></script>
     <script src="assets/vendor/flickity/js/flickity.pkgd.min.js"                   type="text/javascript"></script>
     <script src="assets/vendor/stellar/js/jquery.stellar.min.js"                   type="text/javascript"></script>
     <script src="assets/vendor/isotope/js/jquery.isotope.min.js"                   type="text/javascript"></script>
     <script src="assets/vendor/imagesloaded/js/imagesloaded.pkgd.min.js"           type="text/javascript"></script>
     <script src="assets/vendor/modernizr/js/modernizr.js"                          type="text/javascript"></script>
     <script src="https://maps.google.com/maps/api/js?sensor=false"                 type="text/javascript"></script>
     <script src="assets/custom/js/script.js"                                       type="text/javascript"></script>



    <!-- Fonts -->
     <link href='http://fonts.googleapis.com/css?family=Oxygen:400,700' rel='stylesheet' type='text/css'>


  </head>
  <body>

    <!-- sub-top -->
    <div id="sub-top-light">
      <div class="container">
        <div class="top-contacts">
          <div><a href="#"><i class="fa fa-instagram"></i>@electronic</a></div>
          <div><a href="#"><i class="fa fa-envelope"></i>@electronic</a></div>
          <div><a href="#"><i class="fa fa-phone"></i>@electronic</a></div>
        </div>
        <div class="top-shop"><a href="#"><i class="fa fa-shopping-cart"></i>0</a></div>
      </div>
    </div>
    <!-- /sub-top -->

    <!-- main-top -->
    <header id="header-wrapper" class="sticky-menu-light e-bg-light e-bg-section01" data-stellar-background-ratio="0.5">

       <div class="sticky-header header-light" role="navigation">
          <div class="container">

             <div id="site-title"><h1 class="font-accident-one-bold"><a href="_index.html">Electronic</a></h1></div>

             <div id="mobnav-btn"><i class="fa fa-bars"></i></div>

             <nav id="main-menu" class="site-navigation primary-navigation" role="navigation">

                <ul class="sf-menu clearfix" id="example">
                   <li><a href="index.html">Home</a>
                      <div class="mobnav-subarrow"></div>
                      <ul class="submenu">
                         <li><a href="index01.html">Transparent Header with Dark menu.</a></li>
                         <li><a href="index02.html">Opaque Header with Light menu.</a></li>
                         <li><a href="index03.html">Opaque Header with Dark menu.</a></li>
                      </ul>
                   </li>
                   <li class="current"><a href="about.html">About</a></li>
                   <li><a href="portfolio-3-col-caption.html">Portfolio</a>
                      <div class="mobnav-subarrow"></div>
                      <ul class="submenu">
                         <li><a href="portfolio-4-col-caption.html">Portfolio 4 columns</a>
                            <ul class="submenu">
                               <li><a href="portfolio-4-col.html">Portfolio 4 columns without Captions</a></li>
                               <li><a href="portfolio-4-col-nogutter.html">Portfolio 4 columns without gutters</a></li>
                            </ul>
                         </li>
                         <li><a href="portfolio-3-col-caption.html">Portfolio 3 columns</a>
                            <ul class="submenu">
                               <li><a href="portfolio-3-col.html">Portfolio 3 columns without Captions</a></li>
                               <li><a href="portfolio-3-col-nogutter.html">Portfolio 3 columns without gutters</a></li>
                            </ul>
                         </li>
                         <li><a href="portfolio-2-col-caption.html">Portfolio 2 columns</a>
                            <ul class="submenu">
                               <li><a href="portfolio-2-col.html">Portfolio 2 columns without Captions</a></li>
                               <li><a href="portfolio-2-col-nogutter.html">Portfolio 2 columns without gutters</a></li>
                            </ul>
                         </li>
                         <li><a href="portfolio-item.html">Portfolio Item</a></li>
                      </ul>
                   </li>
                   <li><a href="blog-sb-left.html">Blog</a>
                      <div class="mobnav-subarrow"></div>
                      <ul class="submenu">
                         <li><a href="blog-sb-left.html">Blog with left Sidebar</a></li>
                         <li><a href="blog-sb-right.html">Blog with right Sidebar</a></li>
                         <li><a href="blog-sb-double.html">Blog with double Sidebars</a></li>
                         <li><a href="blog-wo-sb.html">Blog without Sidebar</a></li>
                         <li><a href="blog-post.html">Single Blog Post</a></li>
                      </ul>
                   </li>
                   <li><a href="contacts.html">Contacts</a></li>
                   <li><a href="shop.html">Shop</a>
                      <div class="mobnav-subarrow"></div>
                      <ul class="submenu">
                         <li><a href="shop-item.html">Shop Item</a></li>
                      </ul>
                   </li>
                   <!--<li><a href="#"><i class="fa fa-search"></i></a>-->
                   <!--<div class="mobnav-subarrow"></div>-->
                   <!--<ul class="submenu search-panel">-->
                   <!--<li class="">-->
                   <!--<form class="form-inline" role="form">-->
                   <!--<div class="form-group">-->
                   <!--<input type="text" class="form-control" id="exampleInputEmail2" placeholder="Search">-->
                   <!--<button type="submit" class="btn btn-default">Ok</button>-->
                   <!--</div>-->
                   <!--</form>-->
                   <!--</li>-->
                   <!--</ul>-->
                   <!--</li>-->
                   <li><a href="https://wrapbootstrap.com/theme/electronic-multi-purpose-template-WB081421P" target="_blank">Buy</a></li>
                </ul>

             </nav>

          </div>
       </div>

       <div class="container">
          <div id="page-title">
				 <div class="page-title-details">
					 <h1 class="font-accident-one-bold fontcolor-invert">UI Elements</h1>
					 <ul>
						 <li><a href="#">Home</a>&nbsp;&nbsp;&nbsp;/</li>
						 <li>UI Elements</li>
					 </ul>
				 </div>
          </div>

       </div>

    </header>
    <!-- /main-top -->

    <div id="content">

        <div class="e-block e-block-skin">

           <div class="container">

              <!-- Decorative arrow -->
              <div class="down-arrow">&nbsp;</div>
              <!-- /Decorative arrow -->

              <div class="dividewhite2"></div>

              <!-- Typography -->
              <div class="row">
                 <div class="col-md-12 text-center">
                    <h2 class="font-accident-one-light uppercase">Typography</h2>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <!-- Headings -->
                 <div class="col-md-4">
                    <h1>h1 Heading</h1>
                    <h2>h2 Heading</h2>
                    <h3>h3 Heading</h3>
                    <h4>h4 Heading</h4>
                    <h5>h5 Heading</h5>
                    <h6>h6 Heading</h6>
                 </div>

                 <div class="col-md-4">
                    <h1 class="font-regular-light">h1 Heading</h1>
                    <h2 class="font-regular-light">h2 Heading</h2>
                    <h3 class="font-regular-light">h3 Heading</h3>
                    <h4 class="font-regular-light">h4 Heading</h4>
                    <h5 class="font-regular-light">h5 Heading</h5>
                    <h6 class="font-regular-light">h6 Heading</h6>
                 </div>

                 <div class="col-md-4">
                    <h1 class="font-accident-two-oblique">h1 Heading</h1>
                    <h2 class="font-accident-two-oblique">h2 Heading</h2>
                    <h3 class="font-accident-two-oblique">h3 Heading</h3>
                    <h4 class="font-accident-two-oblique">h4 Heading</h4>
                    <h5 class="font-accident-two-oblique">h5 Heading</h5>
                    <h6 class="font-accident-two-oblique">h6 Heading</h6>
                 </div>
                 <!-- /Headings -->
              </div>

              <div class="dividewhite8"></div>

              <div class="row">
                 <!-- Text block sample with a heading and sub-heading -->
                 <div class="col-md-6">
                    <h3 class="font-accident-two-oblique blogpost-heading">h3 Alternative Heading</h3>
                    <p class="sub-heading">by Alisia Silverstone in Stories, Games</p>
                    <p>
                       Scopula erubescens is a moth of the Geometridae family. It is found in India (Khasia Hills). William Theophilus Dortch (August 23, 1824 – November 21, 1889) was a prominent North Carolina and Confederate States of America politician and lawyer. Dortch was born August 23, 1824 to William Dortch and his wife, Drusilla at his father's plantation, situated in Nash County, North Carolina about 5 miles from the town of Rocky Mount, North Carolina.
                    </p>
                 </div>
                 <div class="col-md-6">
                    <h3 class="font-accident-two-oblique blogpost-heading">h3 Alternative Heading</h3>
                    <p class="sub-heading">by Alisia Silverstone in Stories, Games</p>
                    <p>
                       Scopula erubescens is a moth of the Geometridae family. It is found in India (Khasia Hills). William Theophilus Dortch (August 23, 1824 – November 21, 1889) was a prominent North Carolina and Confederate States of America politician and lawyer. Dortch was born August 23, 1824 to William Dortch and his wife, Drusilla at his father's plantation, situated in Nash County, North Carolina about 5 miles from the town of Rocky Mount, North Carolina.
                    </p>
                 </div>
                 <!-- /Text block sample with a heading and sub-heading -->
              </div>


              <!-- /Typography -->

              <div class="dividewhite8"></div>

              <!-- Content elements -->

              <div class="row">
                 <div class="col-md-12">
                    <h2 class="font-accident-one-light text-center uppercase">Blog post Types</h2>
                    <div class="dividewhite3"></div>
                 </div>
              </div>

              <!-- Blokquote Post -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Blokquote Post</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>


              <div class="row">
                 <div class="col-md-12">

                    <!-- Blog post attributes #1 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #1 -->

                    <!-- Blokquote sample -->
                    <blockquote>
                       <p>
                          Scopula erubescens is a moth of the Geometridae family. It is found in India (Khasia Hills). William Theophilus Dortch (August 23, 1824 – November 21, 1889) was a prominent North Carolina and Confederate States of America politician and lawyer. Dortch was born August 23, 1824 to William Dortch and his wife, Drusilla at his father's plantation, situated in Nash County.
                       </p>
                       <footer>Someone famous in <cite title="Source Title">Source Title</cite></footer>
                    </blockquote>
                    <!-- /Blokquote sample -->

                 </div>
              </div>

              <!-- /Blokquote Post -->


              <div class="dividewhite8"></div>


              <!-- Standard Post -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Standard Post</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">

                 <div class="col-md-12">

                    <!-- Blog post attributes #2 -->
                    <h3 class="post-title">The simple Standard Post</h3>
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->

                    <!-- Post Content -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>
                    <p>
                       Another notable use of CLASP is the University of York, designed by architect Andrew Derbyshire. A later development was known as SCOLA (Second Consortium of Local Authorities) and MACE (Metropolitan Architectural Consortium for Education). The cynics' definition of the CLASP acronym, circulating in the 1970s, was "collection of loosely assembled steel parts". Langwith College, University of York. A notable use of the CLASP system. Vanbrugh College, University of York.
                    </p>
                    <div class="dividewhite2"></div>
                    <button type="button" class="btn btn-default btn-lgr-str">Learn More</button>
                    <!-- /Post Content -->

                 </div>

              </div>

              <!-- /Standard Post -->


              <div class="dividewhite8"></div>


              <!-- Image Post -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Image Post</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-md-12">

                    <h3 class="post-title">The simple Image Post</h3>
                    <!-- Blog post attributes #2 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->
                    <img src="assets/custom/images/pic_01.jpg" class="img-responsive" alt="Responsive image">
                    <div class="dividewhite2"></div>


                    <!-- Post Content -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>

                    <p>
                       Another notable use of CLASP is the University of York, designed by architect Andrew Derbyshire. A later development was known as SCOLA (Second Consortium of Local Authorities) and MACE (Metropolitan Architectural Consortium for Education). The cynics' definition of the CLASP acronym, circulating in the 1970s, was "collection of loosely assembled steel parts". Langwith College, University of York. A notable use of the CLASP system. Vanbrugh College, University of York.
                    </p>
                    <!-- /Post Content -->

                 </div>

              </div>

              <!-- /Image Post -->

              <div class="dividewhite8"></div>

              <!-- In Columns -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Basic Post Types in Columns</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-md-4">

                    <h3 class="post-title">The Blockquote Post</h3>

                    <!-- Blog post attributes #1 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #1 -->

                    <!-- Blokquote sample -->
                    <blockquote>
                       <p>
                          Scopula erubescens is a moth of the Geometridae family. It is found in India (Khasia Hills). William Theophilus Dortch (August 23, 1824 – November 21, 1889) was a prominent North Carolina and Confederate States of America politician and lawyer. Dortch was born August 23, 1824 to William Dortch and his wife, Drusilla at his father's plantation, situated in Nash County.
                       </p>
                       <footer>Someone famous in <cite title="Source Title">Source Title</cite></footer>
                    </blockquote>
                    <!-- /Blokquote sample -->

                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys.
                    </p>
                    <div class="dividewhite4"></div>


                 </div>
                 <div class="col-md-4">
                    <!-- Blog post attributes #2 -->
                    <h3 class="post-title">The simple Standard Post</h3>
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>

                    <p>
                       Another notable use of CLASP is the University of York, designed by architect Andrew Derbyshire. A later development was known as SCOLA (Second Consortium of Local Authorities) and MACE (Metropolitan Architectural Consortium for Education). The cynics' definition of the CLASP acronym, circulating in the 1970s, was "collection of loosely assembled steel parts". Langwith College, University of York. A notable use of the CLASP system. Vanbrugh College, University of York.
                    </p>
                    <div class="dividewhite2"></div>
                    <button type="button" class="btn btn-default btn-lgr-str">Learn More</button>
                    <div class="dividewhite4"></div>

                 </div>

                 <div class="col-md-4">
                    <h3 class="post-title">The simple Image Post</h3>
                    <!-- Blog post attributes #2 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->
                    <img src="assets/custom/images/pic_01.jpg" class="img-responsive" alt="Responsive image">
                    <div class="dividewhite2"></div>

                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing.
                    </p>
                    <div class="dividewhite4"></div>

                 </div>
              </div>


              <!-- /In Columns -->


              <div class="dividewhite8"></div>


              <!-- Gallery Post -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Gallery Post</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-md-12">

                    <h3 class="post-title">The simple Gallery Post</h3>
                    <!-- Blog post attributes #2 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->

                    <!-- FlexSlider -->
                    <div class="flexslider">
                       <ul class="slides">
                          <li><img src="assets/custom/images/pic_03.jpg" alt="x"></li>
                          <li><img src="assets/custom/images/pic_01.jpg" alt="x"></li>
                          <li><img src="assets/custom/images/pic_03.jpg" alt="x"></li>
                       </ul>
                    </div>
                    <div class="dividewhite2"></div>
                    <!-- /FlexSlider -->

                    <!-- Post Content -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>
                    <p>
                       Another notable use of CLASP is the University of York, designed by architect Andrew Derbyshire. A later development was known as SCOLA (Second Consortium of Local Authorities) and MACE (Metropolitan Architectural Consortium for Education). The cynics' definition of the CLASP acronym, circulating in the 1970s, was "collection of loosely assembled steel parts". Langwith College, University of York. A notable use of the CLASP system. Vanbrugh College, University of York.
                    </p>
                    <!-- /Post Content -->

                 </div>

              </div>

              <!-- /Gallery Post -->


              <div class="dividewhite8"></div>

              <!-- Standard Post #2 -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Standard Post #2. Attributes in it's own column.</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">

                 <div class="col-sm-2">

                    <!-- Post Attributes -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data2">
                          <div class="post-date">2014 Jun, 14</div>
                       </div>
                       <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                    </div>
                    <!-- Post Attributes -->

                 </div>

                 <div class="col-sm-10">

                    <!-- Post Title -->
                    <h3 class="post-title">Another simple Standard Post</h3>
                    <div class="dividewhite2"></div>
                    <!-- /Post Title -->

                    <!-- Post Content -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>
                    <p>
                       Another notable use of CLASP is the University of York, designed by architect Andrew Derbyshire. A later development was known as SCOLA (Second Consortium of Local Authorities) and MACE (Metropolitan Architectural Consortium for Education). The cynics' definition of the CLASP acronym, circulating in the 1970s, was "collection of loosely assembled steel parts". Langwith College, University of York. A notable use of the CLASP system. Vanbrugh College, University of York.
                    </p>
                    <div class="dividewhite2"></div>
                    <!-- /Post Content -->

                    <button type="button" class="btn btn-default btn-lgr-str">Learn More</button>

                 </div>


              </div>

              <!-- /Standard Post #2 -->

              <div class="dividewhite8"></div>

              <!-- Chat Post Format -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Chat Post Format</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>
              <div class="row">
                 <div class="col-sm-2">
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data2">
                          <div class="post-date">2014 Jun, 14</div>
                       </div>
                    </div>
                 </div>
                 <div class="col-sm-10">
                    <h3 class="post-title">Our Morning Chat</h3>
                 </div>
              </div>
              <div class="row">
                 <div class="col-sm-2">
                    <p class="chat-companion">Nick</p>
                 </div>
                 <div class="col-sm-10">
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys.
                    </p>
                 </div>
              </div>
              <div class="row">
                 <div class="col-sm-2">
                    <p class="chat-me">Rachel</p>
                 </div>
                 <div class="col-sm-10">
                    <p>
                       Yes!
                    </p>
                 </div>
              </div>
              <div class="row">
                 <div class="col-sm-2">
                    <p class="chat-companion">Nick</p>
                 </div>
                 <div class="col-sm-10">
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys.
                    </p>
                 </div>
              </div>
              <div class="row">
                 <div class="col-sm-2">
                    <p class="chat-me">Rachel</p>
                 </div>
                 <div class="col-sm-10">
                    <p>
                       Shure!
                    </p>
                 </div>
              </div>


              <!-- /Chat Post Format -->

              <div class="dividewhite8"></div>

              <!-- Link Post -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Link Post Format</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-md-12">
                    <!-- Post Title -->
                    <h3 class="post-title">The interesting link</h3>
                    <!-- /Post Title -->

                    <!-- Blog post attributes #2 -->
                    <div class="post-attrib">
                       <div class="comment-baloon">75</div>
                       <div class="post-data">
                          <div class="post-date">2014 Jun, 14</div>
                          <div class="post-author">by Alisia Silverstone in <a href="#">Stories</a>, <a href="#">Games</a></div>
                       </div>
                    </div>
                    <!-- /Blog post attributes #2 -->

                    <div class="link-bg">http://neuethemes.net</div>
                    <div class="dividewhite2"></div>

                    <!-- Post Content -->
                    <p>
                       The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                    </p>
                    <!-- Post Content -->

                 </div>

              </div>

              <!-- /Link Post -->

              <div class="dividewhite8"></div>

              <!-- Standard Post #3. Article. -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Standard Post #3. Article.</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">

                 <div class="col-md-4">
                    <div class="col-xs-3">
                       <img src="assets/custom/images/userpic01.png" alt="Rachel James Johnes" class="img-responsive img-circle">
                       <div class="post-data0">
                          <div class="post-date text-center">2014 Jun, 14</div>
                       </div>
                    </div>
                    <div class="col-xs-9">
                       <h5 class="post-title">In 1965, 7-Eleven bought some of the machines and services</h5>
                       <p>
                          The system utilised prefabricated light gauge steel frames which could be built economically up to a maximum of 4 storeys. The frames were finished in a variety of claddings and their modular nature could be employed to produce architecturally satisfying buildings. Initially developed solely for schools, the system was also used to provide offices and housing. Important examples include many of the Hertfordshire schools, some of which have since been listed.
                       </p>
                       <div class="dividewhite4"></div>
                    </div>
                 </div>

                 <div class="col-md-4">
                    <div class="col-xs-3">
                       <img src="assets/custom/images/userpic02.png" alt="Rachel James Johnes" class="img-responsive img-circle">
                       <div class="post-data0">
                          <div class="post-date text-center">2014 Jun, 14</div>
                       </div>
                    </div>
                    <div class="col-xs-9">
                       <h5 class="post-title">The Room Dedication Program has recently been implemented</h5>
                       <p>
                          The Thayer Hotel is a 151-room "Historic Hotel of America" property located 50 miles north of New York City on the banks of the Hudson River at 674 Thayer Road in West Point, New York on the campus of the United States Military Academy. It is named after Sylvanus Thayer, West Point class of 1808, the "father of the Military Academy." The Thayer Hotel has three main markets: tourists visiting West Point, weddings, and corporate conferences.
                       </p>
                       <div class="dividewhite4"></div>
                    </div>
                 </div>

                 <div class="col-md-4">
                    <div class="col-xs-3">
                       <img src="assets/custom/images/userpic03.png" alt="Rachel James Johnes" class="img-responsive img-circle">
                       <div class="post-data0">
                          <div class="post-date text-center">2014 Jun, 14</div>
                       </div>
                    </div>
                    <div class="col-xs-9">
                       <h5 class="post-title">At next decade, during the Khrushchev Thaw</h5>
                       <p>
                          In 90s, MSU Faculty of Economics has flourished due to economic liberalization and became one of the most desired places to study in (in Russia). The yearly intake gradually increased to 350 students. In 2009, MSU Faculty of Economics moved to a brand new building. Alexander Zhukov, Viktor Sadovnichiy and Vasily Kolesov attended the Official Opening Ceremony.
                       </p>
                       <div class="dividewhite4"></div>
                    </div>
                 </div>

              </div>

              <!-- /Standard Post #3. Article. -->

              <div class="dividewhite8"></div>

              <!-- Pricing Table -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Pricing Tables</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">

                 <div class="col-md-3">
                    <div class="pricing plain">
                       <div class="price-attrs">
                          <div class="price-number font-accident-one-light">01</div>
                          <div class="price-plan"><strong>Trial</strong></div>
                          <div class="price-descr"><strong>Free</strong> for 25 days</div>
                          <div class="price-conclusion">Ideal for Freelancers</div>
                       </div>

                       <ul>
                          <li>5 Web Sites</li>
                          <li>10 Gb Disk Space</li>
                          <li>10 E-mail Boxes</li>
                          <li>FTP Access</li>
                          <li>1 Domain Name</li>
                          <li>Unlimited Traffic</li>
                       </ul>

                       <button type="button" class="btn btn-default btn-gr">Sign Up</button>

                    </div>
                    <div class="dividewhite6"></div>
                 </div>
                 <div class="col-md-3">
                    <div class="pricing plain">
                       <div class="price-attrs">
                          <div class="price-number font-accident-one-light">02</div>
                          <div class="price-plan"><strong>Start</strong></div>
                          <div class="price-descr"><strong>$24.99</strong> per month</div>
                          <div class="price-conclusion">Ideal for Freelancers</div>
                       </div>

                       <ul>
                          <li>5 Web Sites</li>
                          <li>10 Gb Disk Space</li>
                          <li>10 E-mail Boxes</li>
                          <li>FTP Access</li>
                          <li>1 Domain Name</li>
                          <li>Unlimited Traffic</li>
                       </ul>

                       <button type="button" class="btn btn-default btn-gr">Sign Up</button>

                    </div>
                    <div class="dividewhite6"></div>
                 </div>
                 <div class="col-md-3">
                    <div class="pricing proposal">
                       <div class="price-attrs">
                          <div class="price-number font-accident-one-light">03</div>
                          <div class="price-plan"><strong>Business</strong></div>
                          <div class="price-descr"><strong>$49.99</strong> per month</div>
                          <div class="price-conclusion">Ideal for Freelancers</div>
                       </div>

                       <ul>
                          <li>5 Web Sites</li>
                          <li>10 Gb Disk Space</li>
                          <li>10 E-mail Boxes</li>
                          <li>FTP Access</li>
                          <li>1 Domain Name</li>
                          <li>Unlimited Traffic</li>
                       </ul>

                       <button type="button" class="btn btn-default btn-rd">Sign Up</button>

                    </div>
                    <div class="dividewhite6"></div>
                 </div>
                 <div class="col-md-3">
                    <div class="pricing plain">
                       <div class="price-attrs">
                          <div class="price-number font-accident-one-light">04</div>
                          <div class="price-plan"><strong>De Luxe</strong></div>
                          <div class="price-descr"><strong>$99.99</strong> per month</div>
                          <div class="price-conclusion">Ideal for Freelancers</div>
                       </div>

                       <ul>
                          <li>5 Web Sites</li>
                          <li>10 Gb Disk Space</li>
                          <li>10 E-mail Boxes</li>
                          <li>FTP Access</li>
                          <li>1 Domain Name</li>
                          <li>Unlimited Traffic</li>
                       </ul>

                       <button type="button" class="btn btn-default btn-gr">Sign Up</button>

                    </div>
                    <div class="dividewhite6"></div>
                 </div>

              </div>

              <!-- /Pricing Table -->

              <!--     Team      -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Team</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-md-3">

                    <div class="team-member">
                       <figure class="effect-zoe">
                          <div class="team-photo">
                             <img src="assets/custom/images/userpic04.png" alt="Rachel James Johnes" class="img-circle">
                          </div>
                          <div class="team-attrs">
                             <div class="team-name">James Moss</div>
                             <div class="team-position">CEO</div>
                          </div>
                          <div class="team-divider"></div>
                          <div class="team-content">
                             Truong is also the recipient of The George C.
                             Lin Emerging Filmmaker Award from the San Diego
                          </div>
                          <figcaption>
                             <p class="icon-links">
                                <a href="#"><span class="icon-heart"></span></a>
                                <a href="#"><span class="icon-eye"></span></a>
                                <a href="#"><span class="icon-paper-clip"></span></a>
                             </p>
                          </figcaption>
                       </figure>
                    </div>


                    <div class="dividewhite6"></div>

                 </div>
                 <div class="col-md-3">

                    <div class="team-member">
                       <figure class="effect-zoe">
                          <div class="team-photo">
                             <img src="assets/custom/images/userpic01.png" alt="Rachel James Johnes" class="img-circle">
                          </div>
                          <div class="team-attrs">
                             <div class="team-name">Joanne Keppler</div>
                             <div class="team-position">Art-Director</div>
                          </div>
                          <div class="team-divider"></div>
                          <div class="team-content">
                             Truong is also the recipient of The George C.
                             Lin Emerging Filmmaker Award from the San Diego
                          </div>
                          <figcaption>
                             <p class="icon-links">
                                <a href="#"><span class="icon-heart"></span></a>
                                <a href="#"><span class="icon-eye"></span></a>
                                <a href="#"><span class="icon-paper-clip"></span></a>
                             </p>
                          </figcaption>
                       </figure>
                    </div>
                    <div class="dividewhite6"></div>

                 </div>
                 <div class="col-md-3">

                    <div class="team-member">
                       <figure class="effect-zoe">
                          <div class="team-photo">
                             <img src="assets/custom/images/userpic02.png" alt="Rachel James Johnes" class="img-circle">
                          </div>
                          <div class="team-attrs">
                             <div class="team-name">Maria Quinn</div>
                             <div class="team-position">Designer</div>
                          </div>
                          <div class="team-divider"></div>
                          <div class="team-content">
                             Truong is also the recipient of The George C.
                             Lin Emerging Filmmaker Award from the San Diego
                          </div>
                          <figcaption>
                             <p class="icon-links">
                                <a href="#"><span class="icon-heart"></span></a>
                                <a href="#"><span class="icon-eye"></span></a>
                                <a href="#"><span class="icon-paper-clip"></span></a>
                             </p>
                          </figcaption>
                       </figure>
                    </div>
                    <div class="dividewhite6"></div>

                 </div>
                 <div class="col-md-3">

                    <div class="team-member">
                       <figure class="effect-zoe">
                          <div class="team-photo">
                             <img src="assets/custom/images/userpic05.png" alt="Rachel James Johnes" class="img-circle">
                          </div>
                          <div class="team-attrs">
                             <div class="team-name">Rita Vasquez</div>
                             <div class="team-position">Manager</div>
                          </div>
                          <div class="team-divider"></div>
                          <div class="team-content">
                             Truong is also the recipient of The George C.
                             Lin Emerging Filmmaker Award from the San Diego
                          </div>
                          <figcaption>
                             <p class="icon-links">
                                <a href="#"><span class="icon-heart"></span></a>
                                <a href="#"><span class="icon-eye"></span></a>
                                <a href="#"><span class="icon-paper-clip"></span></a>
                             </p>
                          </figcaption>
                       </figure>
                    </div>
                    <div class="dividewhite6"></div>

                 </div>
              </div>

              <!--     /Team      -->


              <div class="row">
                 <div class="col-md-12">
                    <h2 class="font-accident-one-light text-center uppercase">UI Elements</h2>
                    <div class="dividewhite3"></div>
                 </div>
              </div>


              <!-- Buttons -->

              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Buttons</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="dividewhite2"></div>

              <div class="row">
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-blk btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-gr btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-lgr btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-lgr-str btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-grey-str btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-lg btn-rd btn-block">Large button</button>
                 </div>
              </div>

              <div class="dividewhite2"></div>

              <div class="row">
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-blk btn-block">Default button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-gr btn-block">Default button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-lgr btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-lgr-str btn-block">Default button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-grey-str btn-block">Default button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-rd btn-block">Default button</button>
                 </div>
              </div>

              <div class="dividewhite2"></div>

              <div class="row">
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-blk btn-block">Small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-gr btn-block">Small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-lgr btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-lgr-str btn-block">Small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-grey-str btn-block">Small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-rd btn-block">Small button</button>
                 </div>
              </div>

              <div class="dividewhite2"></div>

              <div class="row">
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-blk btn-block">Extra small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-gr btn-block">Extra small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-lgr btn-block">Large button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-lgr-str btn-block">Extra small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-grey-str btn-block">Extra small button</button>
                 </div>
                 <div class="col-md-2">
                    <button type="button" class="btn btn-xs btn-rd btn-block">Extra small button</button>
                 </div>
              </div>


              <!-- /Buttons -->

              <div class="dividewhite8"></div>

              <!-- Tabs -->


              <div class="row">
                 <div class="col-md-12">
                    <h4 class="font-regular-light color-999">Tabs and Accordion</h4>
                    <div class="dividewhite2"></div>
                 </div>
              </div>

              <div class="row">
                 <div class="col-lg-6">

                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                       <li role="presentation" class="active"><a href="#home" role="tab" data-toggle="tab">Home</a></li>
                       <li role="presentation"><a href="#profile" role="tab" data-toggle="tab">Profile</a></li>
                       <li role="presentation"><a href="#messages" role="tab" data-toggle="tab">Messages</a></li>
                       <li role="presentation"><a href="#settings" role="tab" data-toggle="tab">Settings</a></li>
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content">
                       <div role="tabpanel" class="tab-pane fade in active" id="home">
                          In some hypertext, hyperlinks can be bidirectional: they can be followed in two directions, so both ends act as anchors and as targets.
                          More complex arrangements exist, such as many-to-many links. The effect of following a hyperlink may vary with the hypertext system and
                          may sometimes depend on the link itself; for instance, on the World Wide Web, most hyperlinks cause the target document to replace the
                          document being displayed, but some are marked to cause the target document to open in a new window.
                          The 1866 Atlantic hurricane season was originally one of only four Atlantic hurricane seasons in which every known tropical cyclone attained
                          hurricane status
                          Actionstep is an online software as a service product for small and medium-sized businesses with a focus on professional services and

                       </div>
                       <div role="tabpanel" class="tab-pane fade" id="profile">
                          Actionstep is an online software as a service product for small and medium-sized businesses with a focus on professional services and compliance-oriented businesses such as law firms. Actionstep's key points of difference are workflow, all-in-one solution, and integrated accounting.
                       </div>
                       <div role="tabpanel" class="tab-pane fade" id="messages">
                          Cliff Lee (1951 - ) born in Vienna, Austria, in 1951 and raised in Taiwan, is a ceramic artist living in Stevens Township, Pennsylvania and known for his meticulously carved and beautifully glazed porcelain pots. In particular, he is noted for his celadon, oxblood, imperial yellow and oil spot glazes and for carvings in the shape of cabbages, peaches, and lotus flowers.
                       </div>
                       <div role="tabpanel" class="tab-pane fade" id="settings">
                          As a young man, Lee studied medicine at Hershey Medical School and became a successful neurosurgeon before deciding to leave medicine and pursue his passion for studio pottery at the age of 27. Taking ceramics courses at James Madison University in Harrisonburg, Virginia, he decided to pursue this new passion.
                       </div>
                    </div>
                    <!-- /Tabs -->

                 </div>
                 <div class="col-lg-6">

                    <!-- Accordion -->

                    <div class="panel-group" id="accordion">
                       <!-- 1 панель -->
                       <div class="panel panel-default">
                          <!-- Заголовок 1 панели -->
                          <div class="panel-heading">
                             <div class="panel-title font-regular-normal">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">Number one</a>
                             </div>
                          </div>
                          <div id="collapseOne" class="panel-collapse collapse in">
                             <!-- Содержимое 1 панели -->
                             <div class="panel-body">
                                <p>
                                   Every storm but the fourth hurricane affected land during the season. The first hurricane hit Matagorda, Texas in July, the only one of the season to hit the United States as a hurricane. A month later a hurricane made two landfalls in Mexico. The third hurricane of the season formed near Bermuda and was last observed along the southern coast of Newfoundland.
                                </p>
                             </div>
                          </div>
                       </div>
                       <!-- 2 панель -->
                       <div class="panel panel-default">
                          <!-- Заголовок 2 панели -->
                          <div class="panel-heading">
                             <div class="panel-title font-regular-normal">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">Number two</a>
                             </div>
                          </div>
                          <div id="collapseTwo" class="panel-collapse collapse">
                             <!-- Содержимое 2 панели -->
                             <div class="panel-body">
                                <p>
                                   The first hurricane of the season was observed on July 11, when a schooner encountered heavy seas to the south of the Florida Panhandle. As the hurricane moved westward, it remained a short distance off the Gulf Coast of the United States, bringing strong winds to New Orleans on July 12.
                                </p>
                             </div>
                          </div>
                       </div>
                       <!-- 3 панель -->
                       <div class="panel panel-default">
                          <!-- Заголовок 3 панели -->
                          <div class="panel-heading">
                             <div class="panel-title font-regular-normal">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree">Number three</a>
                             </div>
                          </div>
                          <div id="collapseThree" class="panel-collapse collapse">
                             <!-- Содержимое 3 панели -->
                             <div class="panel-body">
                                <p>
                                   On July 15, the hurricane moved ashore near Matagorda Bay in Texas, with winds estimated around 105 mph (165 km/h), or a Category 2 on the Saffir-Simpson scale. At landfall, the minimum barometric pressure was estimated at 969 mbar (28.
                                </p>
                             </div>
                          </div>
                       </div>
                       <!-- 4 панель -->
                       <div class="panel panel-default">
                          <!-- Заголовок 4 панели -->
                          <div class="panel-heading">
                             <div class="panel-title font-regular-normal">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseFour">Number four</a>
                             </div>
                          </div>
                          <div id="collapseFour" class="panel-collapse collapse">
                             <!-- Содержимое 4 панели -->
                             <div class="panel-body">
                                <p>
                                   On August 13, a ship encountered a severe hurricane in the eastern Caribbean Sea. Based on observations, it is estimated the hurricane attained winds of 105 mph (165 km/h). There were no reports for several days, although based on continuity it is estimated the storm passed south of Jamaica on August 15.
                                </p>
                             </div>
                          </div>
                       </div>
                    </div>

                    <!-- /Accordion -->

                 </div>
              </div>

              <!-- /Content elements -->
              <div class="dividewhite8"></div>


           </div>

        </div>

    </div>


    <!-- Google Map -->
    <a class="gm-toggle-link">
      <div class="gm-toggle">
        <i class="fa fa-map-marker"></i>
      </div>
    </a>
    <div id="gm-panel">
      <div id="google-map" class="bigmap"></div>
    </div>
    <!-- /Google Map -->


    <!-- footer -->

    <div id="footer">

        <div class="container">
            <div class="row">
                <div class="col-sm-3">
                    <div class="infoblock">
                        <!-- About Widget -->
                        <h2 class="font-accident-one-bold">HTML Template</h2>
                        <p>Electronic HTML Template is the fastest way to build your own attractive website.</p>

                        <p>Contrast, classic and colorful variations are available in the downloadable archive.</p>

                        <p>Just upload the creative template to your hosting and get the working website right now.</p>
                        <!-- / About Widget -->
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="infoblock">
                        <!-- Contacts Widget -->
                        <h2 class="font-accident-one-bold">Contacts</h2>
                        <div class="contact">
                            <div class="footer-addr">
                                <div class="footer-icon"><i class="fa fa-home"></i></div>
                                <div class="addr-text">12345, Direct Drive - 110, Daytona Beach, Florida, USA </div>
                            </div>
                            <div class="dividewhite1"></div>
                            <div class="footer-addr">
                                <div class="footer-icon"><i class="fa fa-phone"></i></div>
                                <div class="addr-text">1 234 567 67 45</div>
                            </div>
                            <div class="dividewhite1"></div>
                            <div     class="footer-addr">
                                <div class="footer-icon"><i class="fa fa-envelope"></i></div>
                                <div class="addr-text"><a href="mailto:<EMAIL>"><EMAIL></a></div>
                            </div>
                        </div>
                        <!-- / Contacts Widget -->
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="infoblock">
                        <!-- Flickr feed Widget -->
                        <h2 class="font-accident-one-bold">flickr feed</h2>
                        <ul id="basicuse" class="thumbs"></ul>
                        <!-- / Flickr feed Widget -->
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="infoblock">
                        <!-- Follow us Widget -->
                        <h2 class="font-accident-one-bold">Follow us</h2>
                        <div class="follow">
                            <div class="follow-element">
                                <div class="follow-icon"><i class="fa fa-twitter"></i></div>
                                <div class="follow-descr">
                                    <div class="follow-social"><a href="#">twitter</a></div>
                                    <div class="follow-numb">324</div>
                                </div>
                            </div>
                            <div class="dividewhite1"></div>
                            <div class="follow-element">
                                <div class="follow-icon"><i class="fa fa-facebook"></i></div>
                                <div class="follow-descr">
                                    <div class="follow-social"><a href="#">facebook</a></div>
                                    <div class="follow-numb">653</div>
                                </div>
                            </div>
                            <div class="dividewhite1"></div>
                            <div class="follow-element">
                                <div class="follow-icon"><i class="fa fa-linkedin"></i></div>
                                <div class="follow-descr">
                                    <div class="follow-social"><a href="#">linkedin</a></div>
                                    <div class="follow-numb">234</div>
                                </div>
                            </div>
                        </div>
                        <!-- / Follow us Widget -->
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row partners">
                <div class="col-xs-1"><img src="assets/custom/images/partners_01.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_02.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_03.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_04.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_05.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_06.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_07.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_08.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_09.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_10.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_11.png" class="img-responsive" alt="x"></div>
                <div class="col-xs-1"><img src="assets/custom/images/partners_12.png" class="img-responsive" alt="x"></div>
            </div>
        </div>

    </div>
    <div id="copyrights-wrapper">
        <div class="container">
            <div class="copyright">
                <div class="copy-attrs"> © 2014 <a href="http://neuethemes.net">Neuethemes</a>. All rights reserved</div>
                <div class="copy-link">
                    <a href="http://neuethemes.net">Legal Notice</a>
                </div>
                <div class="copy-link">
                    <a href="http://neuethemes.net">Terms & Conditions</a>
                </div>
            </div>
        </div>
    </div>

    <!-- /footer -->


    <!-- Back to Top -->
    <div id="back-top"><a href="#top"></a></div>
    <!-- /Back to Top -->



  </body>
</html>
