/* 导航对齐修复 - 全局样式 */
/* 这个文件用于修复导航条中标题和菜单项的垂直对齐问题 */

/* 主页导航修复 */
#header-wrapper-mp .container.mp-nav {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  min-height: 80px !important;
}

#header-wrapper-mp #site-title {
  display: flex !important;
  align-items: center !important;
  float: none !important;
}

#header-wrapper-mp #site-title h1 {
  margin: 0 !important;
  margin-top: 0 !important;
}

#header-wrapper-mp #main-menu {
  display: flex !important;
  align-items: center !important;
  float: none !important;
  margin-top: 0 !important;
}

#header-wrapper-mp #main-menu ul {
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
}

/* Sticky状态下的修复 */
#header-wrapper-mp .is-sticky #site-title h1 {
  margin-top: 0 !important;
}

#header-wrapper-mp .is-sticky #main-menu {
  margin-top: 0 !important;
}

/* 内页导航修复 */
#header-wrapper .container {
  display: flex !important;
  align-items: center !important;
  min-height: 80px !important;
}

#header-wrapper #site-title {
  display: flex !important;
  align-items: center !important;
}

#header-wrapper #site-title h1 {
  margin: 0 !important;
  margin-top: 0 !important;
}

#header-wrapper #main-menu {
  display: flex !important;
  align-items: center !important;
  margin-top: 0 !important;
}

#header-wrapper #main-menu ul {
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
}

/* 响应式设计保持 */
@media (max-width: 768px) {
  #header-wrapper-mp .container.mp-nav,
  #header-wrapper .container {
    flex-direction: column;
    align-items: flex-start;
    min-height: auto;
  }
  
  #header-wrapper-mp #site-title,
  #header-wrapper #site-title {
    margin-bottom: 10px;
  }
  
  #header-wrapper-mp #main-menu,
  #header-wrapper #main-menu {
    width: 100%;
  }
}
