services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: quantix-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: quantix_root_password_2024
      MYSQL_DATABASE: quantix
      MYSQL_USER: quantix_user
      MY<PERSON>QL_PASSWORD: quantix_password_2024
    ports:
      - '3306:3306'
    volumes:
      - ./mysql_data:/var/lib/mysql
      - ../apiserver/scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - quantix-network
    command: --default-authentication-plugin=mysql_native_password

  # Backend API
  backend:
    build:
      context: ../apiserver
      dockerfile: Dockerfile
    container_name: quantix-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: "3001"
      USE_MYSQL: "true"
      AUTO_FIX_COMPATIBILITY: "true"
      DB_HOST: mysql
      DB_PORT: "3306"
      DB_NAME: quantix
      DB_USER: quantix_user
      DB_PASSWORD: quantix_password_2024
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production-2024
      JWT_EXPIRE: 7d
      FRONTEND_URL: http://localhost:80
      EMAIL_SERVICE: qq
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: ymkfisxoivwrcbcb
      EMAIL_TEST_MODE: skip
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: gpt-3.5-turbo
      MAX_FILE_SIZE: "104857600"
      UPLOAD_PATH: ./uploads
    ports:
      - '3001:3001'
    volumes:
      - ./backend_uploads:/app/uploads
    depends_on:
      - mysql
    networks:
      - quantix-network
    healthcheck:
      test: ['CMD', 'node', 'healthcheck.js']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend (for production deployment)
  frontend:
    build:
      context: ../web
      dockerfile: ../docker/Dockerfile.frontend
      args:
        VITE_API_BASE_URL: /api
    container_name: quantix-frontend
    restart: unless-stopped
    ports:
      - '80:80'
    depends_on:
      - backend
    networks:
      - quantix-network

  # Redis (for caching and session management)
  redis:
    image: redis:7.2-alpine
    container_name: quantix-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - ./redis_data:/data
    networks:
      - quantix-network
    command: redis-server --appendonly yes

# volumes:
#   mysql_data:
#     driver: local
#   backend_uploads:
#     driver: local
#   redis_data:
#     driver: local

networks:
  quantix-network:
    driver: bridge
