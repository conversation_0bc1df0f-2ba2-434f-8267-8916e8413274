<template>
  <div class="complexes-page">
    <!-- Header -->
    <header id="header-wrapper" class="head-overlay sticky-menu-light">
      <div class="sticky-header header-light sticky-overlay nobg">
        <div class="container">
          <div id="site-title">
            <h1 class="font-accident-one-bold">
              <router-link to="/">Quantix Biosciences</router-link>
            </h1>
          </div>

          <div id="mobnav-btn"><i class="fa fa-bars"></i></div>

          <nav id="main-menu" class="site-navigation primary-navigation">
            <ul class="sf-menu clearfix">
              <li><router-link to="/">Home</router-link></li>
              <li><a href="#products">Products</a></li>
              <li><a href="#about">About</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </header>

    <!-- Page Title -->
    <div id="page-title-wrapper" class="page-title-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <div class="page-title">
              <h1>The Complexes</h1>
              <div class="page-title-details">
                <ul>
                  <li><router-link to="/">Home</router-link></li>
                  <li>The Complexes</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div id="main-content-wrapper" class="main-content-wrapper">
      <div class="container">
        <!-- Filter Controls -->
        <div class="row">
          <div class="col-md-12">
            <div class="portfolio-filter">
              <ul class="filter-list">
                <li><a href="#" @click="filterComplexes('all')" :class="{ active: currentFilter === 'all' }">All</a></li>
                <li><a href="#" @click="filterComplexes('metabolic')" :class="{ active: currentFilter === 'metabolic' }">Metabolic</a></li>
                <li><a href="#" @click="filterComplexes('signaling')" :class="{ active: currentFilter === 'signaling' }">Signaling</a></li>
                <li><a href="#" @click="filterComplexes('structural')" :class="{ active: currentFilter === 'structural' }">Structural</a></li>
                <li><a href="#" @click="filterComplexes('regulatory')" :class="{ active: currentFilter === 'regulatory' }">Regulatory</a></li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Complex Grid -->
        <div class="row">
          <div class="col-md-12">
            <div class="portfolio-grid">
              <div class="row">
                <div 
                  v-for="complex in filteredComplexes" 
                  :key="complex.id"
                  class="col-md-3 col-sm-6 portfolio-item"
                  :class="complex.category"
                >
                  <div class="portfolio-item-wrapper">
                    <div class="portfolio-item-image">
                      <img :src="complex.image" :alt="complex.name" class="img-responsive" />
                      <div class="portfolio-item-overlay">
                        <div class="portfolio-item-actions">
                          <a @click="viewComplex(complex)" href="#" class="btn btn-primary">View Details</a>
                          <a :href="complex.downloadUrl" target="_blank" class="btn btn-default">Download</a>
                        </div>
                      </div>
                    </div>
                    <div class="portfolio-item-content">
                      <h4>{{ complex.name }}</h4>
                      <p>{{ complex.description }}</p>
                      <div class="portfolio-item-meta">
                        <span class="category">{{ complex.categoryName }}</span>
                        <span class="size">{{ complex.size }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="row">
          <div class="col-md-12">
            <div class="pagination-wrapper text-center">
              <ul class="pagination">
                <li :class="{ disabled: currentPage === 1 }">
                  <a @click="changePage(currentPage - 1)" href="#">&laquo;</a>
                </li>
                <li v-for="page in totalPages" :key="page" :class="{ active: page === currentPage }">
                  <a @click="changePage(page)" href="#">{{ page }}</a>
                </li>
                <li :class="{ disabled: currentPage === totalPages }">
                  <a @click="changePage(currentPage + 1)" href="#">&raquo;</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Complex Detail Modal -->
    <div v-if="selectedComplex" class="modal fade" id="complexModal" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" @click="closeModal">&times;</button>
            <h4 class="modal-title">{{ selectedComplex.name }}</h4>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <img :src="selectedComplex.image" :alt="selectedComplex.name" class="img-responsive" />
              </div>
              <div class="col-md-6">
                <h5>Description</h5>
                <p>{{ selectedComplex.fullDescription }}</p>
                
                <h5>Properties</h5>
                <ul>
                  <li><strong>Category:</strong> {{ selectedComplex.categoryName }}</li>
                  <li><strong>Size:</strong> {{ selectedComplex.size }}</li>
                  <li><strong>Function:</strong> {{ selectedComplex.function }}</li>
                  <li><strong>Location:</strong> {{ selectedComplex.location }}</li>
                </ul>

                <h5>Related Publications</h5>
                <ul>
                  <li v-for="pub in selectedComplex.publications" :key="pub.id">
                    <a :href="pub.url" target="_blank">{{ pub.title }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="closeModal">Close</button>
            <a :href="selectedComplex.downloadUrl" target="_blank" class="btn btn-primary">Download Data</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer id="footer-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-md-12 text-center">
            <p>&copy; 2024 Quantix Biosciences. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Complex {
  id: number
  name: string
  description: string
  fullDescription: string
  category: string
  categoryName: string
  size: string
  function: string
  location: string
  image: string
  downloadUrl: string
  publications: Array<{
    id: number
    title: string
    url: string
  }>
}

const currentFilter = ref('all')
const currentPage = ref(1)
const itemsPerPage = 12
const selectedComplex = ref<Complex | null>(null)

const complexes = ref<Complex[]>([
  {
    id: 1,
    name: "ATP Synthase Complex",
    description: "Essential enzyme complex for ATP production",
    fullDescription: "ATP synthase is a protein complex that catalyzes the formation of ATP from ADP and inorganic phosphate. It is found in the inner mitochondrial membrane and plays a crucial role in cellular energy production.",
    category: "metabolic",
    categoryName: "Metabolic",
    size: "600 kDa",
    function: "ATP synthesis",
    location: "Mitochondrial inner membrane",
    image: "/assets/custom/images/rs-images/01new.jpg",
    downloadUrl: "/downloads/atp-synthase-data.zip",
    publications: [
      { id: 1, title: "Structure and mechanism of ATP synthase", url: "https://pubmed.ncbi.nlm.nih.gov/example1" }
    ]
  },
  {
    id: 2,
    name: "Ribosome Complex",
    description: "Protein synthesis machinery",
    fullDescription: "The ribosome is a large ribonucleoprotein complex responsible for protein synthesis in all living cells. It translates messenger RNA into polypeptide chains.",
    category: "structural",
    categoryName: "Structural",
    size: "2.5 MDa",
    function: "Protein synthesis",
    location: "Cytoplasm/ER",
    image: "/assets/custom/images/rs-images/02new.jpg",
    downloadUrl: "/downloads/ribosome-data.zip",
    publications: [
      { id: 2, title: "Ribosome structure and function", url: "https://pubmed.ncbi.nlm.nih.gov/example2" }
    ]
  }
  // Add more complexes as needed
])

const filteredComplexes = computed(() => {
  let filtered = complexes.value
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(complex => complex.category === currentFilter.value)
  }
  
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  const filtered = currentFilter.value === 'all' 
    ? complexes.value 
    : complexes.value.filter(complex => complex.category === currentFilter.value)
  return Math.ceil(filtered.length / itemsPerPage)
})

const filterComplexes = (category: string) => {
  currentFilter.value = category
  currentPage.value = 1
}

const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const viewComplex = (complex: Complex) => {
  selectedComplex.value = complex
  // Show modal (you might want to use a proper modal library)
  const modal = document.getElementById('complexModal')
  if (modal) {
    modal.style.display = 'block'
    modal.classList.add('show')
  }
}

const closeModal = () => {
  selectedComplex.value = null
  const modal = document.getElementById('complexModal')
  if (modal) {
    modal.style.display = 'none'
    modal.classList.remove('show')
  }
}

onMounted(() => {
  // Initialize any required functionality
})
</script>

<style scoped>
@import url('/assets/vendor/bootstrap/css/bootstrap.min.css');
@import url('/assets/vendor/fontawesome/css/font-awesome.css');
@import url('/assets/custom/css/style.css');

.complexes-page {
  font-family: 'Arial', sans-serif;
}

.portfolio-filter {
  margin-bottom: 40px;
  text-align: center;
}

.filter-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: inline-block;
}

.filter-list li {
  display: inline-block;
  margin: 0 10px;
}

.filter-list a {
  padding: 10px 20px;
  background: #f5f5f5;
  color: #333;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.filter-list a:hover,
.filter-list a.active {
  background: #de3627;
  color: white;
}

.portfolio-item {
  margin-bottom: 30px;
}

.portfolio-item-wrapper {
  background: white;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.portfolio-item-wrapper:hover {
  transform: translateY(-5px);
}

.portfolio-item-image {
  position: relative;
  overflow: hidden;
}

.portfolio-item-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.portfolio-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-item-wrapper:hover .portfolio-item-overlay {
  opacity: 1;
}

.portfolio-item-actions .btn {
  margin: 0 5px;
}

.portfolio-item-content {
  padding: 20px;
}

.portfolio-item-meta {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.portfolio-item-meta .category {
  background: #de3627;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  margin-right: 10px;
}

#header-wrapper {
  background: rgba(255, 255, 255, 0.95);
}

#page-title-wrapper {
  background: #333;
  color: white;
  padding: 60px 0;
}

#footer-wrapper {
  background: #333;
  color: white;
  padding: 30px 0;
  margin-top: 50px;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1050;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
}

.modal.show {
  display: block;
}
</style>
