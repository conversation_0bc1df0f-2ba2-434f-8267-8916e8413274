<template>
  <div class="complexes-page">
    <!-- sub-top -->
    <div id="sub-top-light">
      <div class="container">
        <div class="top-contacts">
          <div><a href="mailto:<EMAIL>"><i class="fa fa-envelope"></i><EMAIL></a></div>
        </div>
      </div>
    </div>
    <!-- /sub-top -->

    <!-- main-top -->
    <header id="header-wrapper" class="sticky-menu-light e-bg-section05">
      <div class="sticky-header header-light" role="navigation">
        <div class="container">
          <div id="site-title">
            <h1 class="font-accident-one-bold">
              <router-link to="/">Complex subunits connectivity showcase</router-link>
            </h1>
          </div>
        </div>
      </div>
    </header>
    <!-- /main-top -->

    <div id="content">
      <div id="portfolio" class="e-block e-block-skin e-block-centered">
        <!-- Decorative arrow -->
        <div class="down-arrow">&nbsp;</div>
        <!-- /Decorative arrow -->
        <div class="dividewhite2"></div>

        <div class="container">
        </div>

        <blockquote>
          <p>
            pTM and ipTM scores: the predicted template modeling (pTM) score and the interface predicted template modeling (ipTM) score are both derived from a measure called the template modeling (TM) score. This measures the accuracy of the entire structure. A pTM score above 0.5 means the overall predicted fold for the complex might be similar to the true structure. ipTM measures the accuracy of the predicted relative positions of the subunits within the complex. Values higher than 0.8 represent confident high-quality predictions.
          </p>
        </blockquote>

        <div class="container isotope-container">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading protein complexes...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-message">
              <i class="fa fa-exclamation-triangle"></i>
              <p>{{ error }}</p>
              <button @click="initializeComplexes" class="retry-button">Retry</button>
            </div>
          </div>

          <!-- 复合体网格 -->
          <div v-else id="posts" class="row">
            <div
              v-for="(complex, index) in complexes"
              :key="complex.id"
              :id="complex.id.toString()"
              class="item web col-md-3"
            >
              <div class="item-wrap">
                <figure class="effect-goliath captions">
                  <img
                    :src="complex.imagePath"
                    :alt="complex.name + ' Complex'"
                    loading="lazy"
                    @error="handleImageError($event, complex)"
                    @load="handleImageLoad($event, complex)"
                  />
                  <div v-if="complex.loading" class="image-loading">
                    <div class="image-spinner"></div>
                  </div>
                </figure>
                <div class="portfolio-caption">
                  <h4 class="font-accident-one-normal uppercase">
                    <a href="blog-wo-sb_ComplexDescription.html">{{ complex.displayName }}</a>
                  </h4>
                  <div class="complex-info">
                    <small class="text-muted">{{ complex.fileName }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div v-if="!loading && !error" class="stats-container">
            <p class="stats-text">
              <i class="fa fa-info-circle"></i>
              Showing {{ complexes.length }} protein complexes with 3D structural models
            </p>
          </div>
        </div>
      </div>

      <div class="dividewhite10"></div>
    </div>

    <!-- Footer -->
    <AppFooter />
    <!-- /Footer -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AppFooter from '@/components/AppFooter.vue'

// 定义复合体数据类型
interface Complex {
  id: number
  name: string
  displayName: string
  imagePath: string
  fileName: string
  loading?: boolean
  error?: boolean
}

// 响应式数据
const complexes = ref<Complex[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

// GIF文件名列表（从实际目录中获取）
const gifFiles = [
  'abraxas2_shmt1_model_0.gif',
  'acad8_med1_med4_model_0.gif',
  'adipoq_colgalt1_model_0.gif',
  'adipoq_plod3_model_0.gif',
  'akt1_nqo2_model_0.gif',
  'amfr_bag6_model_0.gif',
  'anapc7_cdk2ap1_model_0.gif',
  'angptl4_sdc1_model_0.gif',
  'angptl4_sdc4_model_0.gif',
  'anp32a_anp32b_psma3_model_0.gif',
  'anp32a_psma3_model_0.gif',
  'anp32b_psma3_model_0.gif',
  'anxa2_src_model_0.gif',
  'anxa7_sri_model_0.gif',
  'apaf1_cycs_model_0.gif',
  'apex1_hif1a_model_0.gif',
  'apex1_hif1a_stat3_model_0.gif',
  'aqp4_atp1b1_hepacam_model_0.gif',
  'aqp4_atp1b1_model_0.gif',
  'aqp4_hepacam_model_0.gif'
]

// 将文件名转换为显示名称的函数
const formatDisplayName = (fileName: string): string => {
  // 移除 '_model_0.gif' 后缀
  const baseName = fileName.replace('_model_0.gif', '')
  // 将下划线替换为连字符，并添加 'Complex' 后缀
  return baseName.replace(/_/g, '-') + ' Complex'
}

// 将文件名转换为简短名称的函数
const formatShortName = (fileName: string): string => {
  return fileName.replace('_model_0.gif', '').replace(/_/g, '-')
}

// 初始化复合体数据
const initializeComplexes = async () => {
  try {
    loading.value = true
    error.value = null

    // 模拟异步加载过程
    await new Promise(resolve => setTimeout(resolve, 500))

    complexes.value = gifFiles.map((fileName, index) => ({
      id: index + 1,
      name: formatShortName(fileName),
      displayName: formatDisplayName(fileName),
      imagePath: `/GIF-FPS20-640/${fileName}`,
      fileName: fileName,
      loading: true,
      error: false
    }))

    loading.value = false
    console.log(`✅ 已加载 ${complexes.value.length} 个蛋白质复合体`)
  } catch (err) {
    loading.value = false
    error.value = 'Failed to load protein complexes. Please try again.'
    console.error('❌ 加载复合体数据失败:', err)
  }
}

// 处理图片加载成功
const handleImageLoad = (event: Event, complex: Complex) => {
  complex.loading = false
  complex.error = false
  console.log(`✅ 图片加载成功: ${complex.fileName}`)
}

// 处理图片加载失败
const handleImageError = (event: Event, complex: Complex) => {
  complex.loading = false
  complex.error = true
  console.warn(`⚠️ 图片加载失败: ${complex.fileName}`)

  // 设置备用图片或占位符
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+'
}

// 组件挂载时初始化数据
onMounted(() => {
  initializeComplexes()
})
</script>

<style scoped>
/* Import external CSS styles to match original design */
@import url('/assets/vendor/bootstrap/css/bootstrap.min.css');
@import url('/assets/vendor/fontawesome/css/font-awesome.css');
@import url('/assets/custom/css/style.css');

/* 导航对齐修复 */
#header-wrapper .container {
  display: flex;
  align-items: center;
  min-height: 80px;
}

#header-wrapper #site-title {
  display: flex;
  align-items: center;
}

#header-wrapper #site-title h1 {
  margin: 0 !important;
}

/* 覆盖全局CSS的margin设置 */
#header-wrapper #site-title h1 {
  margin-top: 0 !important;
}

/* 页面特定样式 */
.complexes-page {
  font-family: 'Oxygen', sans-serif;
}

/* 确保容器有正确的样式 */
.container {
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Header样式 */
#header-wrapper {
  background: #fff;
  border-bottom: 1px solid #eee;
}

.sticky-header {
  padding: 20px 0;
}

/* 字体样式 */
.font-accident-one-bold {
  font-weight: bold;
  text-transform: uppercase;
}

.font-accident-one-normal {
  font-weight: normal;
}

/* Portfolio网格样式 */
#portfolio {
  padding: 60px 0;
}

.isotope-container {
  margin-top: 40px;
}

#posts {
  margin: 0 -15px;
}

.item {
  padding: 15px;
  margin-bottom: 30px;
}

.item-wrap {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.item-wrap:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.effect-goliath {
  margin: 0;
  position: relative;
  overflow: hidden;
}

.effect-goliath img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.effect-goliath:hover img {
  transform: scale(1.05);
}

.portfolio-caption {
  padding: 20px;
  text-align: center;
}

.portfolio-caption h4 {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.portfolio-caption h4 a {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.portfolio-caption h4 a:hover {
  color: #007bff;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 60px 20px;
}

.error-message {
  color: #dc3545;
}

.error-message i {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.retry-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background: #0056b3;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.image-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 复合体信息样式 */
.complex-info {
  margin-top: 10px;
}

.text-muted {
  color: #6c757d;
  font-size: 12px;
}

/* 统计信息样式 */
.stats-container {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-text {
  margin: 0;
  color: #495057;
  font-size: 14px;
}

.stats-text i {
  margin-right: 8px;
  color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 480px) {
  .col-md-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .effect-goliath img {
    height: 250px;
  }

  .loading-container,
  .error-container {
    padding: 40px 15px;
  }
}
</style>