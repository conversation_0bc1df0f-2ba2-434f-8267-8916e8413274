<template>
  <div class="complexes-page">
    <!-- sub-top -->
    <div id="sub-top-light">
      <div class="container">
        <div class="top-contacts">
          <div><a href="mailto:<EMAIL>"><i class="fa fa-envelope"></i><EMAIL></a></div>
        </div>
      </div>
    </div>
    <!-- /sub-top -->

    <!-- main-top -->
    <header id="header-wrapper" class="sticky-menu-light e-bg-section05">
      <div class="sticky-header header-light" role="navigation">
        <div class="container">
          <div id="site-title">
            <h1 class="font-accident-one-bold">
              <router-link to="/">Complex subunits connectivity showcase</router-link>
            </h1>
          </div>
        </div>
      </div>
    </header>
    <!-- /main-top -->

    <div id="content">
      <div id="portfolio" class="e-block e-block-skin e-block-centered">
        <!-- Decorative arrow -->
        <div class="down-arrow">&nbsp;</div>
        <!-- /Decorative arrow -->
        <div class="dividewhite2"></div>

        <div class="container">
        </div>

        <blockquote>
          <p>
            pTM and ipTM scores: the predicted template modeling (pTM) score and the interface predicted template modeling (ipTM) score are both derived from a measure called the template modeling (TM) score. This measures the accuracy of the entire structure. A pTM score above 0.5 means the overall predicted fold for the complex might be similar to the true structure. ipTM measures the accuracy of the predicted relative positions of the subunits within the complex. Values higher than 0.8 represent confident high-quality predictions.
          </p>
        </blockquote>

        <div class="container isotope-container">
          <div id="posts" class="row">
            <div id="1" class="item web col-md-3">
              <div class="item-wrap">
                <figure class="effect-goliath captions">
                  <img src="/GIF-FPS20-640/abraxas2_shmt1_model_0.gif" alt="abraxas2-shmt1 Complex"/>
                </figure>
                <div class="portfolio-caption">
                  <h4 class="font-accident-one-normal uppercase">
                    <a href="blog-wo-sb_ComplexDescription.html">abraxas2-shmt1 Complex</a>
                  </h4>
                </div>
              </div>
            </div>

            <div id="2" class="item web col-md-3">
              <div class="item-wrap">
                <figure class="effect-goliath captions">
                  <img src="/GIF-FPS20-640/acad8_med1_med4_model_0.gif" alt="acad8-med1_med4 Complex"/>
                </figure>
                <div class="portfolio-caption">
                  <h4 class="font-accident-one-normal uppercase">
                    <a href="blog-wo-sb_ComplexDescription.html">acad8-med1_med4 Complex</a>
                  </h4>
                </div>
              </div>
            </div>

            <div id="3" class="item web col-md-3">
              <div class="item-wrap">
                <figure class="effect-goliath captions">
                  <img src="/GIF-FPS20-640/adipoq_colgalt1_model_0.gif" alt="adipoq-colgalt1 Complex"/>
                </figure>
                <div class="portfolio-caption">
                  <h4 class="font-accident-one-normal uppercase">
                    <a href="blog-wo-sb_ComplexDescription.html">adipoq-colgalt1 Complex</a>
                  </h4>
                </div>
              </div>
            </div>

            <!-- Note: Only showing complexes with available GIF files -->
            <!-- Additional complex items would be added here when more GIF files are available -->
          </div>
        </div>
      </div>

      <div class="dividewhite10"></div>
    </div>

    <!-- Footer -->
    <AppFooter />
    <!-- /Footer -->
  </div>
</template>

<script setup lang="ts">
import AppFooter from '@/components/AppFooter.vue'
// Component logic here
</script>

<style scoped>
/* Import external CSS styles to match original design */

/* 导航对齐修复 */
#header-wrapper .container {
  display: flex;
  align-items: center;
  min-height: 80px;
}

#header-wrapper #site-title {
  display: flex;
  align-items: center;
}

#header-wrapper #site-title h1 {
  margin: 0 !important;
}

/* 覆盖全局CSS的margin设置 */
#header-wrapper #site-title h1 {
  margin-top: 0 !important;
}
</style>