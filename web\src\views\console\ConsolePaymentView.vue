<template>
  <div class="payment-container">
    <div class="payment-header">
      <h1>💳 {{ $t('paymentPage') }}</h1>
      <button @click="goBack" class="btn-back">← {{ $t('goBack') }}</button>
    </div>

    <div class="payment-content">
      <!-- 订单信息 -->
      <div class="order-info">
        <h2>📋 {{ $t('orderInfo') }}</h2>
        <div class="order-details">
          <p>
            <strong>{{ $t('productName') }}:</strong>
            {{ orderInfo.title || $t('analysisReport') }}
          </p>
          <p>
            <strong>{{ $t('orderAmount') }}:</strong>
            <span class="price">¥{{ orderInfo.amount || 0 }}</span>
          </p>
          <p>
            <strong>{{ $t('orderNumber') }}:</strong> {{ paymentData?.paymentId || "N/A" }}
          </p>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div v-if="!paymentData" class="payment-methods">
        <h2>💰 {{ $t('selectPaymentMethod') }}</h2>
        <p class="payment-description">
          {{ $t('paymentDescription') }} <strong>{{ orderInfo.applicationId }}</strong>
        </p>
        <div class="method-buttons">
          <button
            @click="selectPaymentMethod('wechat')"
            class="method-btn wechat"
            :class="{ active: selectedMethod === 'wechat' }"
          >
            <div class="method-icon">💚</div>
            <div class="method-name">{{ $t('wechatPay') }}</div>
          </button>
          <button
            @click="selectPaymentMethod('alipay')"
            class="method-btn alipay"
            :class="{ active: selectedMethod === 'alipay' }"
          >
            <div class="method-icon">💙</div>
            <div class="method-name">{{ $t('alipay') }}</div>
          </button>
        </div>

        <button
          @click="createPayment"
          :disabled="!selectedMethod || creating"
          class="btn-create-payment"
        >
          {{ creating ? $t('generatePaymentCode') : $t('payNow') }}
        </button>
      </div>

      <!-- 支付二维码 -->
      <div
        v-if="paymentData && paymentData.status === 'pending'"
        class="payment-qrcode"
      >
        <h2>📱 {{ $t('scanToPay') }}</h2>
        <div class="qr-section">
          <div class="qr-container">
            <img
              v-if="paymentData.qrCodeUrl"
              :src="paymentData.qrCodeUrl"
              alt="支付二维码"
              class="qr-image"
            />
            <div v-else class="qr-loading">
              {{ $t('generatingQRCode') }}
            </div>
          </div>
          <div class="qr-info">
            <p class="payment-method-info">
              <span
                v-if="paymentData.paymentMethod === 'wechat'"
                class="method-badge wechat"
              >
                💚 {{ $t('wechatPay') }}
              </span>
              <span
                v-else-if="paymentData.paymentMethod === 'alipay'"
                class="method-badge alipay"
              >
                💙 {{ $t('alipay') }}
              </span>
            </p>
            <p class="amount-info">
              {{ $t('paymentAmount') }}: <strong>¥{{ paymentData.amount }}</strong>
            </p>
            <p class="expire-info">
              {{ $t('orderWillExpire') }} <span class="countdown">{{ countdown }}</span>
            </p>
          </div>
        </div>

        <div class="payment-tips">
          <h3>💡 {{ $t('paymentTips') }}</h3>
          <ul>
            <li v-if="paymentData.paymentMethod === 'wechat'">
              {{ $t('wechatScanTip') }}
            </li>
            <li v-else-if="paymentData.paymentMethod === 'alipay'">
              {{ $t('alipayScanTip') }}
            </li>
            <li>{{ $t('autoRedirectTip') }}</li>
            <li>{{ $t('contactServiceTip') }}</li>
          </ul>
        </div>

        <div class="payment-actions">
          <button
            @click="checkPaymentStatus"
            :disabled="checking"
            class="btn-check"
          >
            {{ checking ? $t('checking') : $t('checkPaymentStatus') }}
          </button>
          <button @click="cancelPayment" class="btn-cancel">{{ $t('cancelPayment') }}</button>
          <button
            v-if="isDevelopment"
            @click="simulatePayment"
            class="btn-simulate"
          >
            {{ $t('simulatePaymentSuccess') }}
          </button>
        </div>
      </div>

      <!-- 支付状态 -->
      <div
        v-if="paymentData && paymentData.status !== 'pending'"
        class="payment-status"
      >
        <div v-if="paymentData.status === 'paid'" class="status-success">
          <div class="status-icon">✅</div>
          <h2>{{ $t('paymentSuccess') }}</h2>
          <p>{{ $t('paymentCompleted') }}</p>
          <button @click="goToDashboard" class="btn-success">{{ $t('goToDownload') }}</button>
        </div>

        <div v-else-if="paymentData.status === 'failed'" class="status-failed">
          <div class="status-icon">❌</div>
          <h2>{{ $t('paymentFailed') }}</h2>
          <p>{{ $t('paymentError') }}</p>
          <button @click="retryPayment" class="btn-retry">{{ $t('retryPayment') }}</button>
        </div>

        <div
          v-else-if="paymentData.status === 'cancelled'"
          class="status-cancelled"
        >
          <div class="status-icon">🚫</div>
          <h2>{{ $t('paymentCancelled') }}</h2>
          <p>{{ $t('paymentCancelledDesc') }}</p>
          <button @click="retryPayment" class="btn-retry">{{ $t('retryPayment') }}</button>
        </div>

        <div
          v-else-if="paymentData.status === 'expired'"
          class="status-expired"
        >
          <div class="status-icon">⏰</div>
          <h2>{{ $t('paymentExpired') }}</h2>
          <p>{{ $t('paymentExpiredDesc') }}</p>
          <button @click="retryPayment" class="btn-retry">{{ $t('retryPayment') }}</button>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { paymentAPI } from "@/services/apiService";
import type { Payment, PaymentData as ApiPaymentData } from "@/types/api";

interface OrderInfo {
  applicationId: number;
  title: string;
  amount: number;
}

interface PaymentData {
  paymentId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  status: string;
  qrCodeUrl: string;
  qrCodeContent: string;
  expiresAt: string;
  description: string;
}

export default defineComponent({
  name: "ConsolePaymentView",
  setup() {
    const router = useRouter();
    const route = useRoute();
    const { t } = useI18n();

    const orderInfo = ref<OrderInfo>({
      applicationId: 0,
      title: "",
      amount: 0,
    });

    const selectedMethod = ref<string>("");
    const paymentData = ref<Payment | null>(null);
    const creating = ref(false);
    const checking = ref(false);
    const errorMessage = ref("");
    const countdown = ref("");

    const isDevelopment = ref(false);

    let statusCheckInterval: number | null = null;
    let countdownInterval: number | null = null;

    const goBack = () => {
      router.push("/console");
    };

    const goToDashboard = () => {
      router.push("/console");
    };

    const selectPaymentMethod = (method: string) => {
      selectedMethod.value = method;
    };

    const createPayment = async () => {
      if (!selectedMethod.value) return;

      creating.value = true;
      errorMessage.value = "";

      try {
        const response = await paymentAPI.createPayment({
          applicationId: orderInfo.value.applicationId,
          paymentMethod: selectedMethod.value,
          amount: orderInfo.value.amount,
        });

        if (response.data.success) {
          paymentData.value = response.data.payment;
          await nextTick();
          startStatusCheck();
          startCountdown();
        } else {
          errorMessage.value = response.data.message || "生成支付码失败";
        }
      } catch (error: any) {
        console.error("Create payment error:", error);

        // 如果是已存在支付的错误，尝试获取现有支付信息
        if (error.response?.status === 400 && error.response?.data?.paymentId) {
          try {
            const statusResponse = await paymentAPI.getPaymentStatus(error.response.data.paymentId);
            if (statusResponse.data.success) {
              paymentData.value = statusResponse.data.payment;
              await nextTick();
              startStatusCheck();
              startCountdown();
              return;
            }
          } catch (statusError) {
            console.error("Get existing payment error:", statusError);
          }
        }

        errorMessage.value = error.response?.data?.message || "网络错误，请稍后重试";
      } finally {
        creating.value = false;
      }
    };



    const checkPaymentStatus = async () => {
      if (!paymentData.value?.paymentId) return;

      checking.value = true;
      try {
        const response = await paymentAPI.getPaymentStatus(paymentData.value.paymentId);
        if (response.data.success) {
          const payment = response.data.payment;
          paymentData.value = { ...paymentData.value, ...payment };

          if (payment.status === 'paid') {
            stopStatusCheck();
            stopCountdown();
            // 跳转到支付成功页面
            router.push({
              name: "console-payment-success",
              query: { paymentId: payment.paymentId },
            });
          }
        }
      } catch (error) {
        console.error('Check payment status error:', error);
      } finally {
        checking.value = false;
      }
    };

    const cancelPayment = async () => {
      if (paymentData.value?.paymentId) {
        try {
          const response = await paymentAPI.cancelPayment(paymentData.value.paymentId);
          if (response.data.success) {
            stopStatusCheck();
            stopCountdown();
            paymentData.value.status = 'cancelled';
          }
        } catch (error) {
          console.error('Cancel payment error:', error);
        }
      }

      // 返回控制台
      router.push("/console");
    };

    const simulatePayment = async () => {
      if (!paymentData.value?.paymentId) return;

      try {
        const response = await paymentAPI.simulatePayment(paymentData.value.paymentId);
        if (response.data.success) {
          // 停止状态检查和倒计时
          stopStatusCheck();
          stopCountdown();

          // 跳转到支付成功页面
          router.push({
            name: "console-payment-success",
            query: { paymentId: paymentData.value.paymentId },
          });
        } else {
          errorMessage.value = response.data.message || "模拟支付失败";
        }
      } catch (error) {
        console.error('Simulate payment error:', error);
        errorMessage.value = "模拟支付失败，请稍后重试";
      }
    };

    const retryPayment = () => {
      paymentData.value = null;
      selectedMethod.value = "";
      errorMessage.value = "";
    };

    // 检查模拟支付是否启用
    const checkSimulateEnabled = async () => {
      try {
        const response = await paymentAPI.checkSimulateEnabled();
        isDevelopment.value = response.data.enabled;
      } catch (error) {
        console.warn('Failed to check simulate payment status:', error);
        isDevelopment.value = false;
      }
    };

    const startStatusCheck = () => {
      if (statusCheckInterval) return;

      statusCheckInterval = window.setInterval(() => {
        checkPaymentStatus();
      }, 3000); // 每3秒检查一次
    };

    const stopStatusCheck = () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
        statusCheckInterval = null;
      }
    };

    const startCountdown = () => {
      if (!paymentData.value?.expiresAt || countdownInterval) return;

      const updateCountdown = () => {
        const now = new Date().getTime();
        const expireTime = new Date(paymentData.value!.expiresAt || '').getTime();
        const timeLeft = expireTime - now;

        if (timeLeft <= 0) {
          countdown.value = t('expired');
          stopCountdown();
          if (paymentData.value) {
            paymentData.value.status = 'expired';
          }
          return;
        }

        const minutes = Math.floor(timeLeft / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        countdown.value = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      };

      updateCountdown();
      countdownInterval = window.setInterval(updateCountdown, 1000);
    };

    const stopCountdown = () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
      }
    };

    onMounted(async () => {
      const { applicationId, amount, title } = route.query;
      if (applicationId && amount) {
        orderInfo.value = {
          applicationId: Number(applicationId),
          amount: Number(amount),
          title: String(title || t('analysisReport')),
        };
        // 检查模拟支付状态
        await checkSimulateEnabled();
      } else {
        router.push("/console");
      }
    });

    onUnmounted(() => {
      stopStatusCheck();
      stopCountdown();
    });

    return {
      orderInfo,
      selectedMethod,
      paymentData,
      creating,
      checking,
      errorMessage,
      countdown,
      isDevelopment,
      goBack,
      goToDashboard,
      selectPaymentMethod,
      createPayment,
      checkPaymentStatus,
      cancelPayment,
      simulatePayment,
      retryPayment,
    };
  },
});
</script>

<style scoped>
/* 简化的样式 */
.payment-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.payment-header {
  background: #007bff;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn-back {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.payment-content {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.order-info,
.payment-methods,
.payment-qrcode,
.payment-status {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.method-buttons {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
}

.method-btn {
  flex: 1;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  text-align: center;
}

.method-btn.active {
  border-color: #007bff;
  background: #f0f8ff;
}

.btn-create-payment {
  width: 100%;
  padding: 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.1rem;
}

.btn-create-payment:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e74c3c;
}

.payment-description {
  color: #666;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.qr-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.qr-loading {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f8f9fa;
  color: #666;
}
</style>
