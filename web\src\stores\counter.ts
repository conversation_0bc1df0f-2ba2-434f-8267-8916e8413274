import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authAPI, userAPI, setAuthToken, getAuthToken } from '@/services/apiService'
import type { User, LoginCredentials, RegisterData, VerificationData } from '@/types/api'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const isLoggedIn = computed(() => isAuthenticated.value && user.value)
  const userProfile = computed(() => user.value || {})
  const subscriptionPlan = computed(() => user.value?.subscriptionPlan || 'free')
  const token = computed(() => getAuthToken())

  // Actions
  const setUser = (userData: User | null) => {
    user.value = userData
    isAuthenticated.value = true
    if (userData) {
      localStorage.setItem('user', JSON.stringify(userData))
    }
  }

  const setToken = (token: string | null) => {
    if (token) {
      localStorage.setItem('authToken', token)
      setAuthToken(token)
    } else {
      localStorage.removeItem('authToken')
      setAuthToken(null)
    }
  }

  const clearUser = () => {
    user.value = null
    isAuthenticated.value = false
    localStorage.removeItem('user')
    localStorage.removeItem('authToken')
    setAuthToken(null)
  }

  const initializeAuth = () => {
    const token = getAuthToken()
    const savedUser = localStorage.getItem('user')

    if (token && savedUser) {
      try {
        user.value = JSON.parse(savedUser)
        isAuthenticated.value = true
        setAuthToken(token)
      } catch (e) {
        clearUser()
      }
    }
  }

  const login = async (credentials: LoginCredentials) => {
    loading.value = true
    error.value = null

    try {
      const response = await authAPI.login(credentials)
      const { token, user: userData } = response.data

      setToken(token) // 保存token到localStorage
      setUser(userData)

      return { success: true, data: userData }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const register = async (userData: RegisterData) => {
    loading.value = true
    error.value = null

    try {
      const response = await authAPI.register(userData)
      return { success: true, data: response.data }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const verifyEmail = async (verificationData: VerificationData) => {
    loading.value = true
    error.value = null

    try {
      const response = await authAPI.verifyEmail(verificationData)
      const { token, user: userData } = response.data

      setToken(token) // 保存token到localStorage
      setUser(userData)

      return { success: true, data: userData }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Email verification failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true

    try {
      await authAPI.logout()
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      clearUser()
      loading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    loading.value = true
    error.value = null

    try {
      const response = await userAPI.updateProfile({ profile: profileData })
      setUser(response.data.user)
      return { success: true, data: response.data.user }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Profile update failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const updatePreferences = async (preferences: any) => {
    loading.value = true
    error.value = null

    try {
      const response = await userAPI.updatePreferences({ preferences })
      // Update user data with new preferences
      if (user.value) {
        user.value = { ...user.value, ...response.data.preferences }
        localStorage.setItem('user', JSON.stringify(user.value))
      }
      return { success: true, data: response.data.preferences }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Preferences update failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchCurrentUser = async () => {
    if (!getAuthToken()) return

    loading.value = true

    try {
      const response = await authAPI.getCurrentUser()
      setUser(response.data.user)
    } catch (err) {
      clearUser()
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    isAuthenticated,
    loading,
    error,

    // Computed
    isLoggedIn,
    userProfile,
    subscriptionPlan,
    token,

    // Actions
    setUser,
    setToken,
    clearUser,
    initializeAuth,
    login,
    register,
    verifyEmail,
    logout,
    updateProfile,
    updatePreferences,
    fetchCurrentUser,
  }
})
