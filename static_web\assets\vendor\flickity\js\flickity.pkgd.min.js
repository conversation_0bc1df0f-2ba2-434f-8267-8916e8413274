/*!
 * Flickity PACKAGED v0.1.0
 * Touch, responsive, flickable galleries
 * http://flickity.metafizzy.co
 * Copyright 2015 Metafizzy
 */

!function(t){function e(){}function i(t){function i(e){e.prototype.option||(e.prototype.option=function(e){t.isPlainObject(e)&&(this.options=t.extend(!0,this.options,e))})}function o(e,i){t.fn[e]=function(o){if("string"==typeof o){for(var r=n.call(arguments,1),l=0,a=this.length;a>l;l++){var h=this[l],c=t.data(h,e);if(c)if(t.isFunction(c[o])&&"_"!==o.charAt(0)){var d=c[o].apply(c,r);if(void 0!==d)return d}else s("no such method '"+o+"' for "+e+" instance");else s("cannot call methods on "+e+" prior to initialization; attempted to call '"+o+"'")}return this}return this.each(function(){var n=t.data(this,e);n?(n.option(o),n._init()):(n=new i(this,o),t.data(this,e,n))})}}if(t){var s="undefined"==typeof console?e:function(t){console.error(t)};return t.bridget=function(t,e){i(e),o(t,e)},t.bridget}}var n=Array.prototype.slice;"function"==typeof define&&define.amd?define("jquery-bridget/jquery.bridget",["jquery"],i):i("object"==typeof exports?require("jquery"):t.jQuery)}(window),function(t){function e(t){return new RegExp("(^|\\s+)"+t+"(\\s+|$)")}function i(t,e){var i=n(t,e)?s:o;i(t,e)}var n,o,s;"classList"in document.documentElement?(n=function(t,e){return t.classList.contains(e)},o=function(t,e){t.classList.add(e)},s=function(t,e){t.classList.remove(e)}):(n=function(t,i){return e(i).test(t.className)},o=function(t,e){n(t,e)||(t.className=t.className+" "+e)},s=function(t,i){t.className=t.className.replace(e(i)," ")});var r={hasClass:n,addClass:o,removeClass:s,toggleClass:i,has:n,add:o,remove:s,toggle:i};"function"==typeof define&&define.amd?define("classie/classie",r):"object"==typeof exports?module.exports=r:t.classie=r}(window),function(){function t(){}function e(t,e){for(var i=t.length;i--;)if(t[i].listener===e)return i;return-1}function i(t){return function(){return this[t].apply(this,arguments)}}var n=t.prototype,o=this,s=o.EventEmitter;n.getListeners=function(t){var e,i,n=this._getEvents();if(t instanceof RegExp){e={};for(i in n)n.hasOwnProperty(i)&&t.test(i)&&(e[i]=n[i])}else e=n[t]||(n[t]=[]);return e},n.flattenListeners=function(t){var e,i=[];for(e=0;e<t.length;e+=1)i.push(t[e].listener);return i},n.getListenersAsObject=function(t){var e,i=this.getListeners(t);return i instanceof Array&&(e={},e[t]=i),e||i},n.addListener=function(t,i){var n,o=this.getListenersAsObject(t),s="object"==typeof i;for(n in o)o.hasOwnProperty(n)&&-1===e(o[n],i)&&o[n].push(s?i:{listener:i,once:!1});return this},n.on=i("addListener"),n.addOnceListener=function(t,e){return this.addListener(t,{listener:e,once:!0})},n.once=i("addOnceListener"),n.defineEvent=function(t){return this.getListeners(t),this},n.defineEvents=function(t){for(var e=0;e<t.length;e+=1)this.defineEvent(t[e]);return this},n.removeListener=function(t,i){var n,o,s=this.getListenersAsObject(t);for(o in s)s.hasOwnProperty(o)&&(n=e(s[o],i),-1!==n&&s[o].splice(n,1));return this},n.off=i("removeListener"),n.addListeners=function(t,e){return this.manipulateListeners(!1,t,e)},n.removeListeners=function(t,e){return this.manipulateListeners(!0,t,e)},n.manipulateListeners=function(t,e,i){var n,o,s=t?this.removeListener:this.addListener,r=t?this.removeListeners:this.addListeners;if("object"!=typeof e||e instanceof RegExp)for(n=i.length;n--;)s.call(this,e,i[n]);else for(n in e)e.hasOwnProperty(n)&&(o=e[n])&&("function"==typeof o?s.call(this,n,o):r.call(this,n,o));return this},n.removeEvent=function(t){var e,i=typeof t,n=this._getEvents();if("string"===i)delete n[t];else if(t instanceof RegExp)for(e in n)n.hasOwnProperty(e)&&t.test(e)&&delete n[e];else delete this._events;return this},n.removeAllListeners=i("removeEvent"),n.emitEvent=function(t,e){var i,n,o,s,r=this.getListenersAsObject(t);for(o in r)if(r.hasOwnProperty(o))for(n=r[o].length;n--;)i=r[o][n],i.once===!0&&this.removeListener(t,i.listener),s=i.listener.apply(this,e||[]),s===this._getOnceReturnValue()&&this.removeListener(t,i.listener);return this},n.trigger=i("emitEvent"),n.emit=function(t){var e=Array.prototype.slice.call(arguments,1);return this.emitEvent(t,e)},n.setOnceReturnValue=function(t){return this._onceReturnValue=t,this},n._getOnceReturnValue=function(){return this.hasOwnProperty("_onceReturnValue")?this._onceReturnValue:!0},n._getEvents=function(){return this._events||(this._events={})},t.noConflict=function(){return o.EventEmitter=s,t},"function"==typeof define&&define.amd?define("eventEmitter/EventEmitter",[],function(){return t}):"object"==typeof module&&module.exports?module.exports=t:o.EventEmitter=t}.call(this),function(t){function e(e){var i=t.event;return i.target=i.target||i.srcElement||e,i}var i=document.documentElement,n=function(){};i.addEventListener?n=function(t,e,i){t.addEventListener(e,i,!1)}:i.attachEvent&&(n=function(t,i,n){t[i+n]=n.handleEvent?function(){var i=e(t);n.handleEvent.call(n,i)}:function(){var i=e(t);n.call(t,i)},t.attachEvent("on"+i,t[i+n])});var o=function(){};i.removeEventListener?o=function(t,e,i){t.removeEventListener(e,i,!1)}:i.detachEvent&&(o=function(t,e,i){t.detachEvent("on"+e,t[e+i]);try{delete t[e+i]}catch(n){t[e+i]=void 0}});var s={bind:n,unbind:o};"function"==typeof define&&define.amd?define("eventie/eventie",s):"object"==typeof exports?module.exports=s:t.eventie=s}(this),function(t){function e(t){if(t){if("string"==typeof n[t])return t;t=t.charAt(0).toUpperCase()+t.slice(1);for(var e,o=0,s=i.length;s>o;o++)if(e=i[o]+t,"string"==typeof n[e])return e}}var i="Webkit Moz ms Ms O".split(" "),n=document.documentElement.style;"function"==typeof define&&define.amd?define("get-style-property/get-style-property",[],function(){return e}):"object"==typeof exports?module.exports=e:t.getStyleProperty=e}(window),function(t){function e(t){var e=parseFloat(t),i=-1===t.indexOf("%")&&!isNaN(e);return i&&e}function i(){}function n(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0,i=r.length;i>e;e++){var n=r[e];t[n]=0}return t}function o(i){function o(){if(!u){u=!0;var n=t.getComputedStyle;if(h=function(){var t=n?function(t){return n(t,null)}:function(t){return t.currentStyle};return function(e){var i=t(e);return i||s("Style returned "+i+". Are you running this code in a hidden iframe on Firefox? See http://bit.ly/getsizebug1"),i}}(),c=i("boxSizing")){var o=document.createElement("div");o.style.width="200px",o.style.padding="1px 2px 3px 4px",o.style.borderStyle="solid",o.style.borderWidth="1px 2px 3px 4px",o.style[c]="border-box";var r=document.body||document.documentElement;r.appendChild(o);var l=h(o);d=200===e(l.width),r.removeChild(o)}}}function l(t){if(o(),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==typeof t&&t.nodeType){var i=h(t);if("none"===i.display)return n();var s={};s.width=t.offsetWidth,s.height=t.offsetHeight;for(var l=s.isBorderBox=!(!c||!i[c]||"border-box"!==i[c]),u=0,p=r.length;p>u;u++){var f=r[u],v=i[f];v=a(t,v);var g=parseFloat(v);s[f]=isNaN(g)?0:g}var m=s.paddingLeft+s.paddingRight,y=s.paddingTop+s.paddingBottom,x=s.marginLeft+s.marginRight,b=s.marginTop+s.marginBottom,C=s.borderLeftWidth+s.borderRightWidth,S=s.borderTopWidth+s.borderBottomWidth,w=l&&d,P=e(i.width);P!==!1&&(s.width=P+(w?0:m+C));var E=e(i.height);return E!==!1&&(s.height=E+(w?0:y+S)),s.innerWidth=s.width-(m+C),s.innerHeight=s.height-(y+S),s.outerWidth=s.width+x,s.outerHeight=s.height+b,s}}function a(e,i){if(t.getComputedStyle||-1===i.indexOf("%"))return i;var n=e.style,o=n.left,s=e.runtimeStyle,r=s&&s.left;return r&&(s.left=e.currentStyle.left),n.left=i,i=n.pixelLeft,n.left=o,r&&(s.left=r),i}var h,c,d,u=!1;return l}var s="undefined"==typeof console?i:function(t){console.error(t)},r=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"];"function"==typeof define&&define.amd?define("get-size/get-size",["get-style-property/get-style-property"],o):"object"==typeof exports?module.exports=o(require("desandro-get-style-property")):t.getSize=o(t.getStyleProperty)}(window),function(t){function e(t){"function"==typeof t&&(e.isReady?t():r.push(t))}function i(t){var i="readystatechange"===t.type&&"complete"!==s.readyState;e.isReady||i||n()}function n(){e.isReady=!0;for(var t=0,i=r.length;i>t;t++){var n=r[t];n()}}function o(o){return"complete"===s.readyState?n():(o.bind(s,"DOMContentLoaded",i),o.bind(s,"readystatechange",i),o.bind(t,"load",i)),e}var s=t.document,r=[];e.isReady=!1,"function"==typeof define&&define.amd?define("doc-ready/doc-ready",["eventie/eventie"],o):"object"==typeof exports?module.exports=o(require("eventie")):t.docReady=o(t.eventie)}(window),function(t){function e(t,e){return t[r](e)}function i(t){if(!t.parentNode){var e=document.createDocumentFragment();e.appendChild(t)}}function n(t,e){i(t);for(var n=t.parentNode.querySelectorAll(e),o=0,s=n.length;s>o;o++)if(n[o]===t)return!0;return!1}function o(t,n){return i(t),e(t,n)}var s,r=function(){if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0,n=e.length;n>i;i++){var o=e[i],s=o+"MatchesSelector";if(t[s])return s}}();if(r){var l=document.createElement("div"),a=e(l,"div");s=a?e:o}else s=n;"function"==typeof define&&define.amd?define("matches-selector/matches-selector",[],function(){return s}):"object"==typeof exports?module.exports=s:window.matchesSelector=s}(Element.prototype),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/utils",["doc-ready/doc-ready","matches-selector/matches-selector"],function(i,n){return e(t,i,n)}):"object"==typeof exports?module.exports=e(t,require("doc-ready"),require("desandro-matches-selector")):t.utils=e(t,t.docReady,t.matchesSelector)}(window,function(t,e,i){function n(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()}var o={};o.extend=function(t,e){for(var i in e)t[i]=e[i];return t},o.modulo=function(t,e){return(t%e+e)%e};var s=Object.prototype.toString;o.isArray=function(t){return"[object Array]"==s.call(t)},o.makeArray=function(t){var e=[];if(o.isArray(t))e=t;else if(t&&"number"==typeof t.length)for(var i=0,n=t.length;n>i;i++)e.push(t[i]);else e.push(t);return e},o.indexOf=Array.prototype.indexOf?function(t,e){return t.indexOf(e)}:function(t,e){for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i;return-1},o.removeFrom=function(t,e){var i=o.indexOf(e,t);-1!=i&&e.splice(i,1)},o.isElement="function"==typeof HTMLElement||"object"==typeof HTMLElement?function(t){return t instanceof HTMLElement}:function(t){return t&&"object"==typeof t&&1==t.nodeType&&"string"==typeof t.nodeName},o.setText=function(){function t(t,i){e=e||(void 0!==document.documentElement.textContent?"textContent":"innerText"),t[e]=i}var e;return t}(),o.getParent=function(t,e){for(;t!=document.body;)if(t=t.parentNode,i(t,e))return t},o.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},o.filterFindElements=function(t,e){t=o.makeArray(t);for(var n=[],s=0,r=t.length;r>s;s++){var l=t[s];if(o.isElement(l))if(e){i(l,e)&&n.push(l);for(var a=l.querySelectorAll(e),h=0,c=a.length;c>h;h++)n.push(a[h])}else n.push(l)}return n},o.debounceMethod=function(t,e,i){var n=t.prototype[e],o=e+"Timeout";t.prototype[e]=function(){var t=this[o];t&&clearTimeout(t);var e=arguments,s=this;this[o]=setTimeout(function(){n.apply(s,e),delete s[o]},i||100)}};var r=t.jQuery;return o.htmlInit=function(t,i){e(function(){for(var e=n(i),o=document.querySelectorAll(".js-"+e),s="data-"+e+"-options",l=0,a=o.length;a>l;l++){var h,c=o[l],d=c.getAttribute(s);try{h=d&&JSON.parse(d)}catch(u){console&&console.error("Error parsing "+s+" on "+c.nodeName.toLowerCase()+(c.id?"#"+c.id:"")+": "+u);continue}var p=new t(c,h);r&&r.data(c,i,p)}})},o}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/cell",["get-size/get-size"],function(i){return e(t,i)}):"object"==typeof exports?module.exports=e(t,require("get-size")):(t.Flickity=t.Flickity||{},t.Flickity.Cell=e(t,t.getSize))}(window,function(t,e){function i(t,e){this.element=t,this.parent=e,this.create()}return i.prototype.create=function(){this.element.style.position="absolute",this.x=0,this.shift=0},i.prototype.destroy=function(){this.element.style.position="";var t=this.parent.originSide;this.element.style[t]=""},i.prototype.getSize=function(){this.size=e(this.element)},i.prototype.setPosition=function(t){this.x=t,this.setDefaultTarget(),this.renderPosition(t)},i.prototype.setDefaultTarget=function(){var t="left"==this.parent.originSide?"marginLeft":"marginRight";this.target=this.x+this.size[t]+this.size.width*this.parent.cellAlign},i.prototype.renderPosition=function(t){var e=this.parent.originSide;this.element.style[e]=this.parent.getPositionValue(t)},i.prototype.wrapShift=function(t){this.shift=t,this.renderPosition(this.x+this.parent.slideableWidth*t)},i.prototype.remove=function(){this.element.parentNode.removeChild(this.element)},i}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/prev-next-button",["./utils"],function(i){return e(t,i)}):"object"==typeof exports?module.exports=e(t,require("./utils")):(t.Flickity=t.Flickity||{},t.Flickity.PrevNextButton=e(t,t.utils))}(window,function(t,e){function i(t,e){this.direction=t,this.parent=e,this._create()}var n="http://www.w3.org/2000/svg",o=function(){function t(){if(void 0!==e)return e;var t=document.createElement("div");return t.innerHTML="<svg/>",e=(t.firstChild&&t.firstChild.namespaceURI)==n}var e;return t}();return i.prototype._create=function(){this.isEnabled=!0,this.isPrevious=-1==this.direction;var t=this.parent.options.rightToLeft?1:-1;if(this.isLeft=this.direction==t,this.element=document.createElement("button"),this.element.className="flickity-prev-next-button",this.element.className+=this.isPrevious?" previous":" next",o()){var e=this.createSVG();this.element.appendChild(e)}else this.setArrowText(),this.element.className+=" no-svg";var i=this;this.onselect=function(){i.update()},this.parent.on("select",this.onselect),this.element.onclick=function(){i.onclick()}},i.prototype.activate=function(){this.parent.element.appendChild(this.element)},i.prototype.deactivate=function(){this.parent.element.removeChild(this.element)},i.prototype.createSVG=function(){var t=document.createElementNS(n,"svg");t.setAttribute("viewBox","0 0 100 100");var e=document.createElementNS(n,"path");e.setAttribute("d","M 50,0 L 60,10 L 20,50 L 60,90 L 50,100 L 0,50 Z"),e.setAttribute("class","arrow");var i=this.isLeft?"translate(15,0)":"translate(85,100) rotate(180)";return e.setAttribute("transform",i),t.appendChild(e),t},i.prototype.setArrowText=function(){var t=this.parent.options,i=this.isLeft?t.leftArrowText:t.rightArrowText;e.setText(this.element,i)},i.prototype.onclick=function(){if(this.isEnabled){this.parent.uiChange();var t=this.isPrevious?"previous":"next";this.parent[t]()}},i.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},i.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},i.prototype.update=function(){if(this.parent.options.wrapAround)return void this.enable();var t=this.isPrevious?0:this.parent.cells.length-1,e=this.parent.selectedIndex==t?"disable":"enable";this[e]()},i}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/page-dots",["eventie/eventie","./utils"],function(i,n){return e(t,i,n)}):"object"==typeof exports?module.exports=e(t,require("eventie"),require("./utils")):(t.Flickity=t.Flickity||{},t.Flickity.PageDots=e(t,t.eventie,t.utils))}(window,function(t,e,i){function n(t){this.parent=t,this._create()}return n.prototype._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickity-page-dots",this.dots=[];var t=this;this.onselect=function(){t.updateSelected()},this.parent.on("select",this.onselect),e.bind(this.holder,"click",this)},n.prototype.activate=function(){this.setDots(),this.updateSelected(),this.parent.element.appendChild(this.holder)},n.prototype.deactivate=function(){this.parent.element.removeChild(this.holder)},n.prototype.setDots=function(){var t=this.parent.cells.length-this.dots.length;t>0?this.addDots(t):0>t&&this.removeDots(-t)},n.prototype.addDots=function(t){for(var e=document.createDocumentFragment(),i=[];t;){var n=document.createElement("li");n.className="dot",e.appendChild(n),i.push(n),t--}this.holder.appendChild(e),this.dots=this.dots.concat(i)},n.prototype.removeDots=function(t){for(var e=this.dots.splice(this.dots.length-t,t),i=0,n=e.length;n>i;i++){var o=e[i];this.holder.removeChild(o)}},n.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot"),this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected"},n.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},n.prototype.onclick=function(t){var e=t.target;if("LI"==e.nodeName){this.parent.uiChange();var n=i.indexOf(this.dots,e);this.parent.select(n)}},n}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/player",[],function(){return e()}):"object"==typeof exports?module.exports=e():(t.Flickity=t.Flickity||{},t.Flickity.Player=e())}(window,function(){function t(t){this.isPlaying=!1,this.parent=t}return t.prototype.play=function(){this.isPlaying=!0,delete this.isPaused,this.tick()},t.prototype.tick=function(){if(this.isPlaying&&!this.isPaused){var t=this.parent.options.autoPlay;t="number"==typeof t?t:3e3;var e=this;this.timeout=setTimeout(function(){e.parent.next(!0),e.tick()},t)}},t.prototype.stop=function(){this.isPlaying=!1,delete this.isPaused,this.clear()},t.prototype.clear=function(){clearTimeout(this.timeout)},t.prototype.pause=function(){this.isPlaying&&(this.isPaused=!0,this.clear())},t.prototype.unpause=function(){this.isPaused&&this.play()},t}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/unipointer",["eventie/eventie"],function(i){return e(t,i)}):"object"==typeof exports?module.exports=e(t,require("eventie")):t.Unipointer=e(t,t.eventie)}(window,function(t,e){function i(){}function n(){}function o(){return!1}n.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},n.prototype.getTouch=function(t){for(var e=0,i=t.length;i>e;e++){var n=t[e];if(n.identifier==this.pointerIdentifier)return n}},n.prototype.bindHandles=function(e){var i;i=t.navigator.pointerEnabled?this.bindPointer:t.navigator.msPointerEnabled?this.bindMSPointer:this.bindMouseTouch,e=void 0===e?!0:!!e;for(var n=0,o=this.handles.length;o>n;n++){var s=this.handles[n];i.call(this,s,e)}},n.prototype.bindPointer=function(t,i){var n=i?"bind":"unbind";e[n](t,"pointerdown",this),t.style.touchAction=i?"none":""},n.prototype.bindMSPointer=function(t,i){var n=i?"bind":"unbind";e[n](t,"MSPointerDown",this),t.style.msTouchAction=i?"none":""},n.prototype.bindMouseTouch=function(t,i){var n=i?"bind":"unbind";e[n](t,"mousedown",this),e[n](t,"touchstart",this),i&&r(t)};var s="attachEvent"in document.documentElement,r=s?function(t){"IMG"==t.nodeName&&(t.ondragstart=o);for(var e=t.querySelectorAll("img"),i=0,n=e.length;n>i;i++){var s=e[i];s.ondragstart=o}}:i;n.prototype.onmousedown=function(t){var e=t.button;e&&0!==e&&1!==e||this._pointerDown(t,t)},n.prototype.ontouchstart=function(t){this._pointerDown(t,t.changedTouches[0])},n.prototype.onMSPointerDown=n.prototype.onpointerdown=function(t){this._pointerDown(t,t)};var l={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"],MSPointerDown:["MSPointerMove","MSPointerUp","MSPointerCancel"]};return n.prototype._pointerDown=function(e,i){this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==i.pointerId?i.pointerId:i.identifier,this._bindPostStartEvents({events:l[e.type],node:e.preventDefault?t:document}),this.pointerDown(e,i),this.emitEvent("pointerDown",[this,e,i]))},n.prototype.pointerDown=i,n.prototype._bindPostStartEvents=function(t){for(var i=0,n=t.events.length;n>i;i++){var o=t.events[i];e.bind(t.node,o,this)}this._boundPointerEvents=t},n.prototype._unbindPostStartEvents=function(){var t=this._boundPointerEvents;if(t&&t.events){for(var i=0,n=t.events.length;n>i;i++){var o=t.events[i];e.unbind(t.node,o,this)}delete this._boundPointerEvents}},n.prototype.onmousemove=function(t){this._pointerMove(t,t)},n.prototype.onMSPointerMove=n.prototype.onpointermove=function(t){t.pointerId==this.pointerIdentifier&&this._pointerMove(t,t)},n.prototype.ontouchmove=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerMove(t,e)},n.prototype._pointerMove=function(t,e){this.pointerMove(t,e),this.emitEvent("pointerMove",[this,t,e])},n.prototype.pointerMove=i,n.prototype.onmouseup=function(t){this._pointerUp(t,t)},n.prototype.onMSPointerUp=n.prototype.onpointerup=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},n.prototype.ontouchend=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerUp(t,e)},n.prototype._pointerUp=function(t,e){this.isPointerDown=!1,delete this.pointerIdentifier,this._unbindPostStartEvents(),this.pointerUp(t,e),this.emitEvent("pointerUp",[this,t,e])},n.prototype.pointerUp=i,n.prototype.onMSPointerCancel=n.prototype.onpointercancel=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},n.prototype.ontouchcancel=function(t){var e=this.getTouch(t.changedTouches);this._pointerUp(t,e)},n.getPointerPoint=function(t){return{x:void 0!==t.pageX?t.pageX:t.clientX,y:void 0!==t.pageY?t.pageY:t.clientY}},n.setPointerPoint=function(t,e){t.x=void 0!==e.pageX?e.pageX:e.clientX,t.y=void 0!==e.pageY?e.pageY:e.clientY},n}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/drag",["./unipointer","classie/classie","eventie/eventie","./utils"],function(i,n,o){return e(t,i,n,o)}):"object"==typeof exports?module.exports=e(t,require("./unipointer"),require("desandro-classie"),require("eventie"),require("./utils")):(t.Flickity=t.Flickity||{},t.Flickity.dragPrototype=e(t,t.Unipointer,t.classie,t.eventie,t.utils))}(window,function(t,e,i,n,o){function s(t){t.preventDefault?t.preventDefault():t.returnValue=!1}var r={};o.extend(r,e.prototype),r.bindDrag=function(){this.options.draggable&&(this.handles=[this.viewport],this.bindHandles(),n.bind(this.viewport,"click",this))},r.unbindDrag=function(){this.options.draggable&&(this.bindHandles(!1),n.unbind(this.viewport,"click",this))};var l={INPUT:!0,A:!0,BUTTON:!0};return r.pointerDown=function(t,n){var o=t.target.nodeName,r="touchstart"==t.type,a=l[o];(!r||r&&!a)&&s(t);var h=document.activeElement;h&&h.blur&&h!=this.element&&h.blur(),this.options.accessibility&&"INPUT"!=o&&this.element.focus(),this.velocity=0,this.pointerDownPoint=e.getPointerPoint(n),this.player.stop(),i.add(this.viewport,"is-pointer-down")},r.pointerMove=function(t,i){var n=e.getPointerPoint(i),o=n.x-this.pointerDownPoint.x;!this.isDragging&&Math.abs(o)>3&&this.dragStart(t,i),this.dragMove(n,t,i)},r.pointerUp=function(t,e){this.isDragging?this.dragEnd(t,e):this.staticClick(t,e),i.remove(this.viewport,"is-pointer-down")},r.dragStart=function(t,i){this.isDragging=!0,this.dragStartPoint=e.getPointerPoint(i),this.dragStartPosition=this.x,this.startAnimation(),this.isPreventingClicks=!0,this.dispatchEvent("dragStart",t,[i])},r.dragMove=function(t,e,i){if(this.isDragging){this.previousDragX=this.x;var n=t.x-this.dragStartPoint.x,o=this.options.rightToLeft?-1:1;if(this.x=this.dragStartPosition+n*o,!this.options.wrapAround&&this.cells.length){var s=Math.max(-this.cells[0].target,this.dragStartPosition);this.x=this.x>s?.5*(this.x-s)+s:this.x;var r=Math.min(-this.getLastCell().target,this.dragStartPosition);this.x=this.x<r?.5*(this.x-r)+r:this.x}this.previousDragMoveTime=this.dragMoveTime,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",e,[i])}},r.dragEnd=function(t,e){this.dragEndFlick(),this.options.freeScroll&&(this.isFreeScrolling=!0);var i=this.dragEndRestingSelect();if(this.options.freeScroll&&!this.options.wrapAround){var n=this.getRestingPosition();this.isFreeScrolling=-n>this.cells[0].target&&-n<this.getLastCell().target}else this.options.freeScroll||i!=this.selectedIndex||(i+=this.dragEndBoostSelect());this.select(i),this.isDragging=!1;var o=this;setTimeout(function(){delete o.isPreventingClicks}),this.dispatchEvent("dragEnd",t,[e])},r.dragEndFlick=function(){if(isFinite(this.previousDragX)){var t=this.dragMoveTime-this.previousDragMoveTime;t/=1e3/60;var e=this.x-this.previousDragX;this.velocity=e/t,delete this.previousDragX}},r.dragEndRestingSelect=function(){var t=this.getRestingPosition(),e=Math.abs(this.getCellDistance(-t,this.selectedIndex)),i=this._getClosestResting(t,e,1),n=this._getClosestResting(t,e,-1),o=i.distance<n.distance?i.index:n.index;return o},r._getClosestResting=function(t,e,i){for(var n=this.selectedIndex,o=1/0,s=this.options.contain&&!this.options.wrapAround?function(t,e){return e>=t}:function(t,e){return e>t};s(e,o)&&(n+=i,o=e,e=this.getCellDistance(-t,n),null!==e);)e=Math.abs(e);return{distance:o,index:n-i}},r.getCellDistance=function(t,e){var i=this.cells.length,n=this.options.wrapAround?o.modulo(e,i):e,s=this.cells[n];if(!s)return null;var r=this.options.wrapAround?this.slideableWidth*Math.floor(e/i):0;return t-(s.target+r)},r.dragEndBoostSelect=function(){var t=this.getCellDistance(-this.x,this.selectedIndex);return t>0&&this.velocity<-1?1:0>t&&this.velocity>1?-1:0},r.onclick=function(t){this.isPreventingClicks&&s(t)},r.staticClick=function(t,e){"INPUT"==t.target.nodeName&&"text"==t.target.type&&t.target.focus(),this.dispatchEvent("staticClick",t,[e])},r}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/animate",["get-style-property/get-style-property","./utils"],function(i,n){return e(t,i,n)}):"object"==typeof exports?module.exports=e(t,require("desandro-get-style-property"),require("./utils")):(t.Flickity=t.Flickity||{},t.Flickity.animatePrototype=e(t,t.getStyleProperty,t.utils))}(window,function(t,e,i){for(var n,o=0,s="webkit moz ms o".split(" "),r=t.requestAnimationFrame,l=t.cancelAnimationFrame,a=0;a<s.length&&(!r||!l);a++)n=s[a],r=r||t[n+"RequestAnimationFrame"],l=l||t[n+"CancelAnimationFrame"]||t[n+"CancelRequestAnimationFrame"];r&&l||(r=function(e){var i=(new Date).getTime(),n=Math.max(0,16-(i-o)),s=t.setTimeout(function(){e(i+n)},n);return o=i+n,s},l=function(e){t.clearTimeout(e)});var h={};h.startAnimation=function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},h.animate=function(){this.applySelectedAttraction();var t=this.x;if(this.updatePhysics(),this.positionSlider(),this.settle(t),this.isAnimating){var e=this;r(function(){e.animate()})}};var c=e("transform"),d=!!e("perspective");return h.positionSlider=function(){var t=this.x;this.options.wrapAround&&(t=i.modulo(t,this.slideableWidth),t-=this.slideableWidth,this.shiftWrapCells(t)),t+=this.cursorPosition,t=this.options.rightToLeft&&c?-t:t;var e=this.getPositionValue(t);c?this.slider.style[c]=d&&this.isAnimating?"translate3d("+e+",0,0)":"translateX("+e+")":this.slider.style[this.originSide]=e},h.positionSliderAtSelected=function(){var t=this.cells[this.selectedIndex];this.x=-t.target,this.positionSlider()},h.getPositionValue=function(t){return this.options.percentPosition?.01*Math.round(t/this.size.innerWidth*1e4)+"%":Math.round(t)+"px"},h.settle=function(t){this.isPointerDown||Math.round(100*this.x)!=Math.round(100*t)||this.restingFrames++,this.restingFrames>2&&(this.isAnimating=!1,delete this.isFreeScrolling,d&&this.positionSlider(),this.dispatchEvent("settle"))},h.shiftWrapCells=function(t){var e=this.cursorPosition+t;this._shiftCells(this.beforeShiftCells,e,-1);var i=this.size.innerWidth-(t+this.slideableWidth+this.cursorPosition);this._shiftCells(this.afterShiftCells,i,1)},h._shiftCells=function(t,e,i){for(var n=0,o=t.length;o>n;n++){var s=t[n],r=e>0?i:0;s.wrapShift(r),e-=s.size.outerWidth}},h._unshiftCells=function(t){if(t&&t.length)for(var e=0,i=t.length;i>e;e++)t[e].wrapShift(0)},h.updatePhysics=function(){this.velocity+=this.accel,this.x+=this.velocity,this.velocity*=this.getFrictionFactor(),this.accel=0},h.applyForce=function(t){this.accel+=t},h.getFrictionFactor=function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},h.getRestingPosition=function(){return this.x+this.velocity/(1-this.getFrictionFactor())},h.applySelectedAttraction=function(){if(!this.isPointerDown&&!this.isFreeScrolling&&this.cells.length){var t=this.cells[this.selectedIndex],e=this.options.wrapAround?this.slideableWidth*Math.floor(this.selectedIndex/this.cells.length):0,i=-1*(t.target+e)-this.x,n=i*this.options.selectedAttraction;this.applyForce(n)}},h}),function(t,e){"function"==typeof define&&define.amd?define("flickity/js/cell-change",["./utils"],function(i){return e(t,i)}):"object"==typeof exports?module.exports=e(t,require("./utils")):(t.Flickity=t.Flickity||{},t.Flickity.cellChangePrototype=e(t,t.utils))}(window,function(t,e){function i(t){for(var e=document.createDocumentFragment(),i=0,n=t.length;n>i;i++){var o=t[i];e.appendChild(o.element)}return e}var n={};return n.insert=function(t,e){var n=this._makeCells(t);if(n&&n.length){var o=this.cells.length;e=void 0===e?o:e;var s=i(n),r=e==o;if(r)this.slider.appendChild(s);else{var l=this.cells[e].element;this.slider.insertBefore(s,l)}if(0===e)this.cells=n.concat(this.cells);else if(r)this.cells=this.cells.concat(n);else{var a=this.cells.splice(e,o-e);this.cells=this.cells.concat(n).concat(a)}this._sizeCells(n),this._cellAddedRemoved(e)}},n.append=function(t){this.insert(t,this.cells.length)},n.prepend=function(t){this.insert(t,0)},n.remove=function(t){for(var i=this.getCells(t),n=0,o=i.length;o>n;n++){var s=i[n];s.remove(),e.removeFrom(s,this.cells)}i.length&&this._cellAddedRemoved(0)},n._cellAddedRemoved=function(t){this.pageDots&&this.pageDots.setDots(),this.selectedIndex=Math.max(0,Math.min(this.cells.length-1,this.selectedIndex)),this.cellChange(t)},n.cellSizeChange=function(t){var i=this.getCell(t);if(i){i.getSize();var n=e.indexOf(this.cells,i);this.cellChange(n)}},n.cellChange=function(t){t=t||0,this._positionCells(t),this._getWrapShiftCells(),this.setContainerSize(),this.options.freeScroll?this.positionSlider():this.select(this.selectedIndex)},n}),function(t,e){if("function"==typeof define&&define.amd)define(["classie/classie","eventEmitter/EventEmitter","eventie/eventie","get-size/get-size","flickity/js/utils","flickity/js/cell","flickity/js/prev-next-button","flickity/js/page-dots","flickity/js/player","flickity/js/drag","flickity/js/animate","flickity/js/cell-change"],function(i,n,o,s,r,l,a,h,c,d,u,p){return e(t,i,n,o,s,r,l,a,h,c,d,u,p)});else if("object"==typeof exports)module.exports=e(t,require("desandro-classie"),require("wolfy87-eventemitter"),require("eventie"),require("get-size"),require("./utils"),require("./cell"),require("./prev-next-button"),require("./page-dots"),require("./player"),require("./drag"),require("./animate"),require("./cell-change"));else{var i=t.Flickity;t.Flickity=e(t,t.classie,t.EventEmitter,t.eventie,t.getSize,t.utils,i.Cell,i.PrevNextButton,i.PageDots,i.Player,i.dragPrototype,i.animatePrototype,i.cellChangePrototype)}}(window,function(t,e,i,n,o,s,r,l,a,h,c,d,u){function p(t,e){for(;t.children.length;)e.appendChild(t.children[0])}function f(t,e){var i=s.getQueryElement(t);return i?(this.element=i,v&&(this.$element=v(this.element)),this.options=s.extend({},this.constructor.defaults),this.option(e),void this._create()):void(m&&m.error("Bad element for Flickity: "+(i||t)))}var v=t.jQuery,g=t.getComputedStyle,m=t.console,y=t.imagesLoaded,x=0,b={};f.defaults={accessibility:!0,freeScrollFriction:.075,friction:.28,cellAlign:"center",draggable:!0,percentPosition:!0,pageDots:!0,prevNextButtons:!0,resizeBound:!0,selectedAttraction:.025,leftArrowText:"←",rightArrowText:"→"},s.extend(f.prototype,i.prototype),f.prototype._create=function(){var e=this.guid=++x;this.element.flickityGUID=e,b[e]=this,this.selectedIndex=this.options.initialIndex||0,this.restingFrames=0,this.x=0,this.velocity=0,this.accel=0,this.originSide=this.options.rightToLeft?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickity-viewport",this._createSlider(),this.options.prevNextButtons&&(this.prevButton=new l(-1,this),this.nextButton=new l(1,this)),this.options.pageDots&&(this.pageDots=new a(this)),this.player=new h(this),(this.options.resizeBound||this.options.watchCSS)&&n.bind(t,"resize",this),this.options.watchCSS?this.watchCSS():this.activate()
},f.prototype.option=function(t){s.extend(this.options,t)},f.prototype.activate=function(){this.isActive||(this.isActive=!0,e.add(this.element,"flickity-enabled"),p(this.element,this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.getSize(),this.reloadCells(),this.setContainerSize(),this.prevButton&&this.prevButton.activate(),this.nextButton&&this.nextButton.activate(),this.pageDots&&this.pageDots.activate(),this.options.autoPlay&&(this.player.play(),n.bind(this.element,"mouseenter",this)),this.positionSliderAtSelected(),this.select(this.selectedIndex),this.imagesLoaded(),this.bindDrag(),this.options.accessibility&&(this.element.tabIndex=0,n.bind(this.element,"keydown",this)))},f.prototype._createSlider=function(){var t=document.createElement("div");t.className="flickity-slider",t.style[this.originSide]=0,this.slider=t},f.prototype.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(this.cells),this._getWrapShiftCells(),this.setContainerSize()},f.prototype._makeCells=function(t){for(var e=s.filterFindElements(t,this.options.cellSelector),i=[],n=0,o=e.length;o>n;n++){var l=e[n],a=new r(l,this);i.push(a)}return i},f.prototype.getLastCell=function(){return this.cells[this.cells.length-1]},f.prototype.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},f.prototype._positionCells=function(t){this.maxCellHeight=t?this.maxCellHeight||0:0;var e=0;if(t>0){var i=this.cells[t-1];e=i.x+i.size.outerWidth}for(var n,o=this.cells.length,s=t;o>s;s++)n=this.cells[s],n.setPosition(e),e+=n.size.outerWidth,this.maxCellHeight=Math.max(n.size.outerHeight,this.maxCellHeight);this.slideableWidth=e,this._containCells()},f.prototype._sizeCells=function(t){for(var e=0,i=t.length;i>e;e++){var n=t[e];n.getSize()}},f.prototype.getSize=function(){this.size=o(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign};var C={center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}};f.prototype.setCellAlign=function(){var t=C[this.options.cellAlign];this.cellAlign=t?t[this.originSide]:this.options.cellAlign},f.prototype.setContainerSize=function(){this.viewport.style.height=this.maxCellHeight+"px"},f.prototype._getWrapShiftCells=function(){if(this.options.wrapAround){this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells);var t=this.cursorPosition,e=this.cells.length-1;this.beforeShiftCells=this._getGapCells(t,e,-1),t=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(t,0,1)}},f.prototype._getGapCells=function(t,e,i){for(var n=[];t>0;){var o=this.cells[e];if(!o)break;n.push(o),e+=i,t-=o.size.outerWidth}return n},f.prototype._containCells=function(){if(this.options.contain&&!this.options.wrapAround)for(var t=this.getLastCell(),e=this.options.rightToLeft?"marginLeft":"marginRight",i=this.slideableWidth-t.size[e],n=i-this.size.innerWidth*(1-this.cellAlign),o=0,s=this.cells.length;s>o;o++){var r=this.cells[o];r.setDefaultTarget(),r.target=Math.max(r.target,this.cursorPosition),r.target=Math.min(r.target,n)}},f.prototype.dispatchEvent=function(t,e,i){var n=[e].concat(i);if(this.emitEvent(t,n),v&&this.$element)if(e){var o=v.Event(e);o.type=t,this.$element.trigger(o,i)}else this.$element.trigger(t,i)},f.prototype.select=function(t,e){this.isActive&&(this.options.wrapAround&&(0>t?this.x-=this.slideableWidth:t>=this.cells.length&&(this.x+=this.slideableWidth)),(this.options.wrapAround||e)&&(t=s.modulo(t,this.cells.length)),this.cells[t]&&(this.selectedIndex=t,this.setSelectedCell(),this.startAnimation(),this.dispatchEvent("select")))},f.prototype.previous=function(t){this.select(this.selectedIndex-1,t)},f.prototype.next=function(t){this.select(this.selectedIndex+1,t)},f.prototype.updatePrevNextButtons=function(){this.prevButton&&this.prevButton.update(),this.nextButton&&this.nextButton.update()},f.prototype.setSelectedCell=function(){this._removeSelectedCellClass(),this.selectedCell=this.cells[this.selectedIndex],e.add(this.selectedCell.element,"is-selected")},f.prototype._removeSelectedCellClass=function(){this.selectedCell&&e.remove(this.selectedCell.element,"is-selected")},f.prototype.uiChange=function(){this.player.stop(),delete this.isFreeScrolling},f.prototype.imagesLoaded=function(){function t(t,i){var n=e.getCell(i.img),o=n.element||s.getParent(i.img,".flickity-slider > *");e.cellSizeChange(o)}if(this.options.imagesLoaded&&y){var e=this;y(this.slider).on("progress",t)}},f.prototype.getCell=function(t){for(var e=0,i=this.cells.length;i>e;e++){var n=this.cells[e];if(n.element==t)return n}},f.prototype.getCells=function(t){t=s.makeArray(t);for(var e=[],i=0,n=t.length;n>i;i++){var o=t[i],r=this.getCell(o);r&&e.push(r)}return e},f.prototype.onresize=function(){this.watchCSS(),this.resize()},s.debounceMethod(f,"onresize",150),f.prototype.resize=function(){this.isActive&&(this.getSize(),this.options.wrapAround&&(this.x=s.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setContainerSize(),this.positionSliderAtSelected())};var S=f.supportsConditionalCSS=function(){var t;return function(){if(void 0!==t)return t;if(!g)return void(t=!1);var e=document.createElement("style"),i=document.createTextNode('body:after { content: "foo"; display: none; }');e.appendChild(i),document.head.appendChild(e);var n=g(document.body,":after").content;return t=-1!=n.indexOf("foo"),document.head.removeChild(e),t}}();return f.prototype.watchCSS=function(){var t=this.options.watchCSS;if(t){var e=S();if(!e){var i="fallbackOn"==t?"activate":"deactivate";return void this[i]()}var n=g(this.element,":after").content;-1!=n.indexOf("flickity")?this.activate():this.deactivate()}},f.prototype.onkeydown=function(t){if(this.options.accessibility&&(!document.activeElement||document.activeElement==this.element))if(37==t.keyCode){var e=this.options.rightToLeft?"next":"previous";this.uiChange(),this[e]()}else if(39==t.keyCode){var i=this.options.rightToLeft?"previous":"next";this.uiChange(),this[i]()}},f.prototype.onmouseenter=function(){this.player.pause(),n.bind(this.element,"mouseleave",this)},f.prototype.onmouseleave=function(){this.player.unpause(),n.unbind(this.element,"mouseleave",this)},f.prototype.deactivate=function(){if(this.isActive){e.remove(this.element,"flickity-enabled");for(var t=0,i=this.cells.length;i>t;t++){var o=this.cells[t];o.destroy()}this._removeSelectedCellClass(),this.element.removeChild(this.viewport),p(this.slider,this.element),this.prevButton&&this.prevButton.deactivate(),this.nextButton&&this.nextButton.deactivate(),this.pageDots&&this.pageDots.deactivate(),this.player.stop(),this.unbindDrag(),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),n.unbind(this.element,"keydown",this)),this.isActive=!1,this.isAnimating=!1}},f.prototype.destroy=function(){this.deactivate(),(this.options.resizeBound||this.options.watch)&&n.unbind(t,"resize",this),delete this.element.flickityGUID,delete b[this.guid]},s.extend(f.prototype,c),s.extend(f.prototype,d),s.extend(f.prototype,u),f.data=function(t){t=s.getQueryElement(t);var e=t&&t.flickityGUID;return e&&b[e]},s.htmlInit(f,"flickity"),v&&v.bridget&&v.bridget("flickity",f),f.Cell=r,f.PrevNextButton=l,f.PageDots=a,f.Player=h,f});