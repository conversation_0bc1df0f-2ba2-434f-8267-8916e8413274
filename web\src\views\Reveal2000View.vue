<template>
  <div class="reveal2000-page">
    <!-- Header -->
    <header id="header-wrapper" class="head-overlay sticky-menu-light">
      <div class="sticky-header header-light sticky-overlay nobg">
        <div class="container">
          <div id="site-title">
            <h1 class="font-accident-one-bold">
              <router-link to="/">Quantix Biosciences</router-link>
            </h1>
          </div>

          <div id="mobnav-btn"><i class="fa fa-bars"></i></div>

          <nav id="main-menu" class="site-navigation primary-navigation">
            <ul class="sf-menu clearfix">
              <li><router-link to="/">Home</router-link></li>
              <li><a href="#products">Products</a></li>
              <li><a href="#about">About</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </header>

    <!-- Page Title -->
    <div id="page-title-wrapper" class="page-title-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <div class="page-title">
              <h1>Reveal 2000</h1>
              <div class="page-title-details">
                <ul>
                  <li><router-link to="/">Home</router-link></li>
                  <li>Reveal 2000</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div id="main-content-wrapper" class="main-content-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-md-8">
            <!-- Product Images -->
            <div class="product-images">
              <div class="flexslider">
                <ul class="slides">
                  <li>
                    <img src="/assets/custom/images/slider/slide1.jpg" alt="Reveal 2000" />
                  </li>
                  <li>
                    <img src="/assets/custom/images/slider/slide2.jpg" alt="Reveal 2000 Detail" />
                  </li>
                  <li>
                    <img src="/assets/custom/images/slider/slide3.jpg" alt="Reveal 2000 Detail" />
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <!-- Product Info -->
            <div class="product-info">
              <h2>Reveal 2000</h2>
              <div class="product-price">
                <span class="price">Contact for Pricing</span>
              </div>
              
              <div class="product-description">
                <p>The Reveal 2000 is our flagship protein complex analysis system, designed for high-throughput proteomics research and clinical applications.</p>
                
                <h4>Key Features:</h4>
                <ul>
                  <li>High-resolution protein complex detection</li>
                  <li>Automated sample processing</li>
                  <li>Advanced data analysis software</li>
                  <li>Scalable throughput options</li>
                  <li>GMP-compliant design</li>
                </ul>

                <h4>Applications:</h4>
                <ul>
                  <li>Drug discovery and development</li>
                  <li>Biomarker identification</li>
                  <li>Clinical diagnostics</li>
                  <li>Academic research</li>
                </ul>
              </div>

              <div class="product-actions">
                <button @click="requestQuote" class="btn btn-primary btn-lg">Request Quote</button>
                <button @click="downloadBrochure" class="btn btn-default btn-lg">Download Brochure</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="row">
          <div class="col-md-12">
            <div class="product-tabs">
              <ul class="nav nav-tabs">
                <li class="active"><a href="#specifications" data-toggle="tab">Specifications</a></li>
                <li><a href="#documentation" data-toggle="tab">Documentation</a></li>
                <li><a href="#support" data-toggle="tab">Support</a></li>
              </ul>
              
              <div class="tab-content">
                <div class="tab-pane active" id="specifications">
                  <h3>Technical Specifications</h3>
                  <table class="table table-striped">
                    <tbody>
                      <tr>
                        <td><strong>Throughput</strong></td>
                        <td>Up to 2000 samples per day</td>
                      </tr>
                      <tr>
                        <td><strong>Detection Range</strong></td>
                        <td>1 kDa - 10 MDa</td>
                      </tr>
                      <tr>
                        <td><strong>Sample Volume</strong></td>
                        <td>10-100 μL</td>
                      </tr>
                      <tr>
                        <td><strong>Dimensions</strong></td>
                        <td>120 x 80 x 60 cm</td>
                      </tr>
                      <tr>
                        <td><strong>Power Requirements</strong></td>
                        <td>220-240V, 50/60Hz</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <div class="tab-pane" id="documentation">
                  <h3>Documentation</h3>
                  <ul>
                    <li><a href="/downloads/reveal2000-manual.pdf" target="_blank">User Manual</a></li>
                    <li><a href="/downloads/reveal2000-installation.pdf" target="_blank">Installation Guide</a></li>
                    <li><a href="/downloads/reveal2000-protocols.pdf" target="_blank">Standard Protocols</a></li>
                    <li><a href="/downloads/reveal2000-troubleshooting.pdf" target="_blank">Troubleshooting Guide</a></li>
                  </ul>
                </div>
                
                <div class="tab-pane" id="support">
                  <h3>Technical Support</h3>
                  <p>Our technical support team is available to help you get the most out of your Reveal 2000 system.</p>
                  <ul>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Phone:</strong> +46 70 475 1821</li>
                    <li><strong>Hours:</strong> Monday-Friday, 9:00-17:00 CET</li>
                  </ul>
                  <p>For urgent technical issues, please contact our 24/7 emergency support line.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer id="footer-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-md-12 text-center">
            <p>&copy; 2024 Quantix Biosciences. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const requestQuote = () => {
  const subject = encodeURIComponent('Quote Request - Reveal 2000')
  const body = encodeURIComponent(`Dear Quantix Biosciences Team,

I am interested in receiving a quote for the Reveal 2000 system. Please provide information about:

- Pricing and configuration options
- Delivery timeline
- Installation and training services
- Maintenance and support packages

Company: 
Contact Person: 
Email: 
Phone: 

Thank you for your time.

Best regards,`)
  
  const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`
  window.location.href = mailtoLink
}

const downloadBrochure = () => {
  // Create download link for brochure
  const link = document.createElement('a')
  link.href = '/downloads/reveal2000-brochure.pdf'
  link.download = 'Reveal2000-Brochure.pdf'
  link.target = '_blank'
  
  fetch(link.href, { method: 'HEAD' })
    .then(response => {
      if (response.ok) {
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        alert('Brochure is being prepared. Please contact us for more information.')
      }
    })
    .catch(() => {
      alert('Brochure is being prepared. Please contact us for more information.')
    })
}
</script>

<style scoped>
@import url('/assets/vendor/bootstrap/css/bootstrap.min.css');
@import url('/assets/vendor/fontawesome/css/font-awesome.css');
@import url('/assets/custom/css/style.css');

.reveal2000-page {
  font-family: 'Arial', sans-serif;
}

.product-images {
  margin-bottom: 30px;
}

.product-info {
  padding: 20px;
  background: #f9f9f9;
  border-radius: 5px;
}

.product-price {
  margin: 20px 0;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #de3627;
}

.product-actions {
  margin-top: 30px;
}

.product-actions .btn {
  margin-right: 10px;
  margin-bottom: 10px;
}

.product-tabs {
  margin-top: 50px;
}

.tab-content {
  padding: 30px 0;
}

#header-wrapper {
  background: rgba(255, 255, 255, 0.95);
}

#page-title-wrapper {
  background: #333;
  color: white;
  padding: 60px 0;
}

#footer-wrapper {
  background: #333;
  color: white;
  padding: 30px 0;
  margin-top: 50px;
}
</style>
