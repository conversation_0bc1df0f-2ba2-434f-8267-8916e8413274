<template>
  <div class="quantix-homepage">
    <!-- Loader -->
    <div id="loader-wrapper" v-if="isLoading">
      <div id="loader"></div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
    <!-- /Loader -->

    <!-- Revolution Slider -->
    <article class="slider-revolution">
      <div class="tp-banner-container">
        <div class="tp-banner head-overlay">
          <ul>
            <!-- SLIDE 1 -->
            <li class="slide-item active" data-bg="/assets/custom/images/rs-images/FPM.003.png">
              <div class="slide-content">
                <div class="slide-title">COMFIDENT</div>
                <div class="slide-subtitle">Technology for high-throughput protein complex analysis</div>
                <a href="https://www.quantix.se/downloads/COMFIDENT_flyer.pdf" target="_blank" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
            <!-- SLIDE 2 -->
            <li class="slide-item" data-bg="/assets/custom/images/rs-images/FPM.004.png">
              <div class="slide-content">
                <div class="slide-title">Invented in Sweden</div>
                <div class="slide-subtitle">Engineered in China</div>
                <a href="https://www.quantix.se/downloads/COMFIDENT_flyer.pdf" target="_blank" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
            <!-- SLIDE 3 -->
            <li class="slide-item" data-bg="/assets/custom/images/rs-images/FPM.005.png">
              <div class="slide-content">
                <div class="slide-title">The Reveal panels</div>
                <div class="slide-subtitle">Adding thousands of protein complexes assays to your proteomics data</div>
                <a href="https://www.quantix.se/downloads/COMFIDENT_flyer.pdf" target="_blank" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
          </ul>
        </div>

        <!-- Header and Menu Overlay Mode -->
        <header id="header-wrapper-mp" class="head-overlay sticky-menu-light">
          <div class="sticky-header header-dark sticky-overlay nobg">
            <div class="container mp-nav">
              <div id="site-title">
                <h1 class="font-accident-one-bold">
                  <a href="/">Quantix Biosciences</a>
                </h1>
              </div>

              <div id="mobnav-btn"><i class="fa fa-bars"></i></div>

              <nav id="main-menu" class="site-navigation primary-navigation">
                <ul class="sf-menu clearfix">
                  <li>
                    <a href="#products">Products and Services</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><a href="#reveal2000">Reveal 2000</a></li>
                      <li><a href="#reveal3000">Reveal 3000 (Coming Soon)</a></li>
                    </ul>
                  </li>
                  <li>
                    <a href="#complexes">The Complexes</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><a href="#complex-descriptions">Descriptions of the complexes</a></li>
                      <li><a href="#subunits-showcase">Subunits connectivity showcase</a></li>
                    </ul>
                  </li>
                  <li>
                    <a href="#downloads">Downloads</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><a href="/downloads/COMFIDENT_flyer.pdf">Flyer for the COMFIDENT technology</a></li>
                      <li><a href="/downloads/REVEAL2000_flyer.pdf">Flyer for the Reveal 2000 panel</a></li>
                    </ul>
                  </li>
                  <!-- 后台管理入口 -->
                  <li class="admin-access">
                    <a href="#admin">管理后台</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><a @click="goToUserLogin" href="javascript:void(0)">用户登录</a></li>
                      <li><a @click="goToAdminLogin" href="javascript:void(0)">管理员登录</a></li>
                    </ul>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </header>
        <!-- /Header and Menu Overlay Mode -->
      </div>
    </article>
    <!-- /Revolution Slider -->

    <div id="content">
      <!-- Counts Block -->
      <div class="counts e-block-ins e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="count-container row">
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-paper"></div></div>
              <span class="integers digit font-accident-one-light">2,251</span>
              <div class="count-text">protein complexes analyzed</div>
            </div>
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-map"></div></div>
              <span class="integers digit font-accident-one-light">57,094</span>
              <div class="count-text">samples analyzed</div>
            </div>
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-paper-clip"></div></div>
              <span class="integers digit font-accident-one-light">3</span>
              <div class="count-text">publications submitted</div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Counts Block -->

      <!-- Download Block -->
      <div class="e-block-ins e-block-centered e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="row width-80 e-centered">
            <div class="col-md-8 text-center wow fadeIn">
              <h4 class="font-accident-one-medium">Have some questions about our products?</h4>
            </div>
            <div class="col-md-4 text-center wow fadeIn">
              <a href="mailto:<EMAIL>?subject=Query" class="btn btn-lg btn-gr">Send a query</a>
            </div>
          </div>
        </div>
      </div>
      <!-- /Download Block -->

      <!-- Details Block -->
      <div class="hp-details e-block e-block-light e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="row">
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-content-right"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Integrated Approach</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    We combine proteomics detection technologies with statistical deconvolution methods, providing a unique method for analyzing protein complex abundance. This integration broadens the scope of proteomics to include complexome analysis, enabling more detailed understanding of biological systems.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-stack"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Unique Complex Fingerprint Matrix (CFM)</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our COMFIDENT technology generates a CFM by spiking synthetic protein complexes into standard human plasma samples. This matrix shows how each protein readout changes with each complex, providing a detailed profile of protein interactions within complexes.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-image"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Statistical Deconvolution</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our technique uses statistical modeling to decompose protein readouts into various complexes. This allows for the estimation of complex scores, reflecting the complexome profile in the sample.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-air-play"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">High-Throughput and Reproducibility</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    COMFIDENT addresses the challenge of quantifying protein complexes, particularly at a population scale, by using established proteomics assays. This not only simplifies the process but also ensures high throughput and reproducibility in complex score estimation.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-esc"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Wide Applicability and User-Friendliness</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our method is designed to be high-throughput, comprehensive, and user-friendly, making it suitable for large-scale phenotyping of protein complexes. It's adaptable for various scales and types of proteomic analysis.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-moon"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Facilitation of Biomarker Discovery</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    COMFIDENT allows scientists to identify crucial protein complex biomarkers in low-abundance biological fluids, contributing significantly to clinical diagnosis and a deeper understanding of health and disease processes. The accumulated data from COMFIDENT enriches the field of complexome analysis, offering new insights into the proteome.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Details Block -->
    </div>

    <!-- Blog Section -->
    <div id="blog" class="e-block e-block-skin">
      <div class="container">
        <!-- Decorative arrow -->
        <div class="down-arrow">&nbsp;</div>
        <!-- /Decorative arrow -->

        <div class="dividewhite6"></div>

        <div id="primary" role="main">
          <article class="post">
            <!-- Standard Post -->
            <h3 class="post-title"><a href="#blog-post">Development of the COMFIDENT technology</a></h3>
            <div class="post-attrib">
              <div class="comment-baloon">1</div>
              <div class="post-data">
                <div class="post-date">Dec 31, 2023</div>
                <div class="post-author">by the Quantix Core Team</div>
              </div>
            </div>

            <!-- Post Content -->
            <p>
              In 2017, our team from <a href="http://ki.se">Karolinska Institutet</a> developed COMplexome FIngerprint DEconvolutioN Technology (COMFIDENT), an innovative workflow combining statistical modeling with experimental protein synthesis techniques for analyzing protein complexes. This method requires a comprehensive high-throughput proteomics assay for each specific protein complex. Following the completion of these experiments, the resultant Complex Fingerprint Matrix (CFM) enables the evaluation of high-dimensional protein complexes using advanced proteomics data.
            </p>
            <p>
              For the past five years, our team has meticulously conducted protein synthesis experiments to generate a high-quality CFM for approximately 2,000 verified protein complexes. In our initial application of this technology, we assessed 1,600 complexes in more than 30,000 individuals. The findings were remarkable, uncovering previously unknown biomarkers for complex diseases that were undetectable using existing proteomic assays.
            </p>
            <p>
              For detailed information, see our <a href="http://www.quantix.se/downloads/COMFIDENT_flyer.pdf">Flyer</a> for the introduction of COMFIDENT and our <a href="http://examples.quantix.se/showcase.pdf">Showcase</a> for the analysis results.
            </p>
            <div class="dividewhite2"></div>
            <a role="button" href="#blog-post" class="btn btn-default btn-lgr-str">Learn More</a>
            <!-- /Post Content -->
          </article>

          <article class="post">
            <!-- Standard Post -->
            <h3 class="post-title"><a href="#blog-post">Advancing the proteomics field</a></h3>
            <div class="post-attrib">
              <div class="comment-baloon">2</div>
              <div class="post-data">
                <div class="post-date">Jan 7, 2024</div>
                <div class="post-author">by the Quantix Core Team</div>
              </div>
            </div>

            <!-- Post Content -->
            <p>
              The proteomics market is currently experiencing significant growth, indicating a promising potential for technologies like COMFIDENT. As of 2024, the global proteomics market is projected to reach around USD 27.60 billion and is expected to grow at a compound annual growth rate (CAGR) of 14.6%, reaching approximately USD 72.9 billion by 2028. This growth is driven by factors such as the rising demand for personalized medicine, increasing research and development expenditure, and technological advancements in the field.
            </p>
            <p>
              In terms of market segmentation, the reagents and consumables segment holds a substantial share, accounting for more than 70.30% of the global revenue in 2021. This segment is also anticipated to exhibit the fastest growth rate during the projected period. This growth can be attributed to the extensive use of reagents and consumables in various biological research settings, combined with technological developments in instruments like 2-D electrophoresis protein analyzers, which improve research efficiency.
            </p>
            <p>
              The clinical diagnostics segment dominates the proteomics industry, with a significant share of more than 51.65% in 2021. This dominance is due to the high adoption of protein analysis in clinical research for identifying disease biomarkers and risk factors, which opens new opportunities for prevention and early intervention of diseases.
            </p>
            <p>
              Given these market dynamics, the COMFIDENT technology, with its focus on high-dimensional protein complex analysis, could be well-positioned to capitalize on the growing proteomics market. Its potential to uncover novel biomarkers for complex diseases aligns with the current market trend towards personalized medicine and clinical diagnostics. Furthermore, the demand for advanced and efficient diagnostic tools and drug discovery platforms is on the rise, which could further justify the potential profitability and market size for COMFIDENT.
            </p>
            <div class="dividewhite2"></div>
            <a role="button" href="#blog-post" class="btn btn-default btn-lgr-str">Learn More</a>
            <!-- /Post Content -->
          </article>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="col-sm-3 wow fadeIn">
            <div class="infoblock">
              <h2 class="font-accident-one-bold">What we do</h2>
              <p>Revealing the Human Complexome</p>
            </div>
          </div>
          <div class="col-sm-3 wow fadeIn">
            <div class="infoblock">
              <h2 class="font-accident-one-bold">What we can</h2>
              <p>Quantix provide novel assays for thousands of human protein complexes.</p>
            </div>
          </div>
          <div class="col-sm-3 wow fadeIn">
            <div class="infoblock">
              <h2 class="font-accident-one-bold">Contacts</h2>
              <div class="contact">
                <div class="footer-addr">
                  <div class="footer-icon"><i class="fa fa-home"></i></div>
                  <div class="addr-text">Stockholm, Sweden</div>
                </div>
                <div class="dividewhite1"></div>
                <div class="footer-addr">
                  <div class="footer-icon"><i class="fa fa-phone"></i></div>
                  <div class="addr-text">+46 70 475 1821 (email preferred)</div>
                </div>
                <div class="dividewhite1"></div>
                <div class="footer-addr">
                  <div class="footer-icon"><i class="fa fa-envelope"></i></div>
                  <div class="addr-text"><a href="mailto:<EMAIL>"><EMAIL></a></div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-sm-3 wow fadeIn">
            <div class="infoblock">
              <h2 class="font-accident-one-bold">Follow us</h2>
              <div class="follow">
                <div class="follow-element">
                  <div class="follow-icon"><i class="fa fa-twitter"></i></div>
                  <div class="follow-descr">
                    <div class="follow-social"><a href="#">twitter</a></div>
                    <div class="follow-numb">324</div>
                  </div>
                </div>
                <div class="dividewhite1"></div>
                <div class="follow-element">
                  <div class="follow-icon"><i class="fa fa-facebook"></i></div>
                  <div class="follow-descr">
                    <div class="follow-social"><a href="#">facebook</a></div>
                    <div class="follow-numb">653</div>
                  </div>
                </div>
                <div class="dividewhite1"></div>
                <div class="follow-element">
                  <div class="follow-icon"><i class="fa fa-linkedin"></i></div>
                  <div class="follow-descr">
                    <div class="follow-social"><a href="#">linkedin</a></div>
                    <div class="follow-numb">234</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="copyrights-wrapper">
      <div class="container">
        <div class="copyright wow fadeIn">
          <div class="copy-attrs">© 2024 <a href="http://neuethemes.net" target="_blank">Quantix BioSciences</a>. All rights reserved</div>
          <div class="copy-link">
            <a href="http://neuethemes.net" target="_blank">Legal Notice</a>
          </div>
          <div class="copy-link">
            <a href="http://neuethemes.net" target="_blank">Terms & Conditions</a>
          </div>
        </div>
      </div>
    </div>
    <!-- /Footer -->

    <!-- Back to Top -->
    <div id="back-top"><a href="#top"></a></div>
    <!-- /Back to Top -->
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'HomeLandingView',
  setup() {
    const router = useRouter()
    const isLoading = ref(true)
    const currentSlide = ref(0)
    const slideInterval = ref<NodeJS.Timeout | null>(null)

    const goToUserLogin = () => {
      router.push('/login?type=user')
    }

    const goToAdminLogin = () => {
      router.push('/login?type=admin')
    }

    const startSlideshow = () => {
      slideInterval.value = setInterval(() => {
        const slides = document.querySelectorAll('.slide-item')
        if (slides.length > 0) {
          slides[currentSlide.value].classList.remove('active')
          currentSlide.value = (currentSlide.value + 1) % slides.length
          slides[currentSlide.value].classList.add('active')

          // Update background
          const newBg = slides[currentSlide.value].getAttribute('data-bg')
          if (newBg) {
            const banner = document.querySelector('.tp-banner')
            if (banner) {
              (banner as HTMLElement).style.backgroundImage = `url(${newBg})`
            }
          }
        }
      }, 5000) // Change slide every 5 seconds
    }

    const stopSlideshow = () => {
      if (slideInterval.value) {
        clearInterval(slideInterval.value)
        slideInterval.value = null
      }
    }

    onMounted(() => {
      // Hide loader after a short delay
      setTimeout(() => {
        isLoading.value = false
      }, 1000)

      // Start slideshow after loader is hidden
      setTimeout(() => {
        startSlideshow()
      }, 1500)

      // Load external scripts and styles
      const loadExternalResources = () => {
        // Load Bootstrap CSS
        const bootstrapCSS = document.createElement('link')
        bootstrapCSS.rel = 'stylesheet'
        bootstrapCSS.href = '/assets/vendor/bootstrap/css/bootstrap.min.css'
        document.head.appendChild(bootstrapCSS)

        // Load FontAwesome CSS
        const fontAwesomeCSS = document.createElement('link')
        fontAwesomeCSS.rel = 'stylesheet'
        fontAwesomeCSS.href = '/assets/vendor/fontawesome/css/font-awesome.css'
        document.head.appendChild(fontAwesomeCSS)

        // Load custom CSS
        const customCSS = document.createElement('link')
        customCSS.rel = 'stylesheet'
        customCSS.href = '/assets/custom/css/style.css'
        document.head.appendChild(customCSS)

        // Load jQuery
        const jqueryScript = document.createElement('script')
        jqueryScript.src = '/assets/vendor/jquery/js/jquery-1.10.1.min.js'
        document.head.appendChild(jqueryScript)

        // Load Bootstrap JS
        jqueryScript.onload = () => {
          const bootstrapScript = document.createElement('script')
          bootstrapScript.src = '/assets/vendor/bootstrap/js/bootstrap.min.js'
          document.head.appendChild(bootstrapScript)
        }
      }

      loadExternalResources()
    })

    onUnmounted(() => {
      stopSlideshow()
    })

    return {
      isLoading,
      goToUserLogin,
      goToAdminLogin
    }
  },
})
</script>

<style scoped>
/* Import external stylesheets */
@import url('/assets/vendor/bootstrap/css/bootstrap.min.css');
@import url('/assets/vendor/fontawesome/css/font-awesome.css');
@import url('/assets/custom/css/style.css');

.quantix-homepage {
  font-family: 'Arial', sans-serif;
  color: #333;
  background-color: #fff;
  position: relative;
  min-height: 100vh;
}

/* Loader Styles */
#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: #fff;
}

#loader {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-section {
  position: fixed;
  top: 0;
  width: 51%;
  height: 100%;
  background: #fff;
  z-index: 1000;
  transform: translateX(0);
}

.loader-section.section-left {
  left: 0;
}

.loader-section.section-right {
  right: 0;
}

/* Slider Styles */
.slider-revolution {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.tp-banner-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.tp-banner {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('/assets/custom/images/rs-images/FPM.003.png');
  transition: background-image 1s ease-in-out;
}

.tp-banner ul {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}

.slide-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slide-item.active {
  opacity: 1;
}

.slide-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 20px;
}

.slide-title {
  font-size: 56px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-subtitle {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Header Styles */
#header-wrapper-mp {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
}

.sticky-header {
  background: transparent;
  padding: 15px 0;
}

.container.mp-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

#site-title h1 {
  margin: 0;
  font-size: 24px;
}

#site-title h1 a {
  color: white;
  text-decoration: none;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

#mobnav-btn {
  display: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.site-navigation ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  gap: 30px;
}

.site-navigation ul li {
  position: relative;
}

.site-navigation ul li a {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.site-navigation ul li a:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.site-navigation ul li.admin-access a {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.site-navigation ul li.admin-access a:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Submenu Styles */
.submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  min-width: 200px;
  list-style: none;
  margin: 0;
  padding: 10px 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border-radius: 5px;
}

.site-navigation ul li:hover .submenu {
  opacity: 1;
  visibility: visible;
}

.submenu li {
  width: 100%;
}

.submenu li a {
  display: block;
  padding: 10px 20px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.submenu li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 12px 30px;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s ease;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  border: none;
}

.btn-wh {
  background-color: white;
  color: #333;
}

.btn-wh:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.btn-gr {
  background-color: #28a745;
  color: white;
}

.btn-gr:hover {
  background-color: #218838;
}

.btn-lg {
  padding: 15px 35px;
  font-size: 18px;
}

.hero-section {
  background: url('@/assets/images/FPM.005.png') no-repeat center center/cover;
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: fixed; /* 更改为固定定位 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0; /* 将z-index设置为0，确保在所有内容之下 */
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3); /* 调整遮罩层透明度 */
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 20px;
  padding-top: 0; /* 调整内边距 */
}

.hero-content h1 {
  font-size: 56px;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-content p {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
}

.btn-hero {
  background-color: #4caf50; /* 调整按钮颜色 */
  color: white;
  padding: 15px 35px;
  border: none;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-hero:hover {
  background-color: #45a049; /* 调整按钮悬停颜色 */
}

.features-section,
.about-section,
.contact-section,
.footer {
  padding-top: 80px; /* 调整各section的顶部内边距 */
}

.home-landing-container {
  overflow-x: hidden;
}

.hero-section {
  background: url('@/assets/images/FPM.005.png') no-repeat center center/cover;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: relative; /* 更改为相对定位 */
  margin-top: 0; /* 移除顶部外边距 */
  padding-top: 60px; /* 调整内边距，为导航栏留出空间 */
  /* z-index: -1; 将z-index设置为负值，确保在所有内容之下 */
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* 遮罩层 */
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 20px;
}

.hero-content h1 {
  font-size: 56px;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-content p {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
}

.btn-hero {
  background-color: var(--color-success);
  color: white;
  padding: 15px 35px;
  border: none;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-hero:hover {
  background-color: #218838;
}

.features-section,
.about-section,
.contact-section {
  padding: 80px 50px;
  text-align: center;
  background-color: var(--color-background-white);
  margin-bottom: 20px;
  box-shadow: var(--shadow-light);
}

.features-section h2,
.about-section h2,
.contact-section h2 {
  font-size: 36px;
  margin-bottom: 50px;
  color: var(--color-primary);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  background-color: var(--color-light);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-10px);
}

.feature-item h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--color-dark);
}

.feature-item p {
  font-size: 16px;
  color: var(--color-text-medium);
  line-height: 1.7;
}

.about-section p,
.contact-section p {
  font-size: 18px;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  color: var(--color-text-medium);
}

.footer {
  background-color: var(--color-dark);
  color: white;
  text-align: center;
  padding: 25px 0;
  font-size: 15px;
}

.admin-link {
  margin-top: 15px;
}

.admin-login-link {
  color: #ccc;
  text-decoration: none;
  font-size: 12px;
  padding: 5px 10px;
  border: 1px solid #555;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.admin-login-link:hover {
  color: white;
  border-color: #888;
  background-color: rgba(255, 255, 255, 0.1);
}

.init-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border-color: transparent !important;
  font-weight: 600;
}

.init-link:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.qa-section {
  background-color: #f9f9f9;
  padding: 80px 50px;
  text-align: center;
}

.qa-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.qa-section p {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.btn-qa {
  background-color: #007bff;
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 5px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-qa:hover {
  background-color: #0056b3;
}

.about-section {
  padding: 80px 50px;
  text-align: center;
  background-color: #fff;
}

.about-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.about-section p {
  font-size: 18px;
  color: #666;
  line-height: 1.8;
  max-width: 900px;
  margin: 0 auto;
}

.contact-section {
  background-color: #f0f0f0;
  padding: 80px 50px;
  text-align: center;
}

.contact-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.contact-section p {
  font-size: 18px;
  color: #666;
  margin-bottom: 10px;
}

.footer {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
}
</style>
