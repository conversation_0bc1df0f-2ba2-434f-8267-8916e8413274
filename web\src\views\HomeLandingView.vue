<template>
  <div class="quantix-homepage">
    <!-- Loader -->
    <div id="loader-wrapper" v-if="isLoading">
      <div id="loader"></div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
    <!-- /Loader -->

    <!-- Revolution Slider -->
    <article class="slider-revolution">
      <div class="tp-banner-container">
        <div class="tp-banner head-overlay">
          <ul>
            <!-- SLIDE 1 -->
            <li class="slide-item active" data-bg="/assets/custom/images/rs-images/FPM.003.png">
              <div class="slide-content">
                <div class="slide-title">COMFIDENT</div>
                <div class="slide-subtitle">Technology for high-throughput protein complex analysis</div>
                <a @click="downloadFlyer" href="javascript:void(0)" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
            <!-- SLIDE 2 -->
            <li class="slide-item" data-bg="/assets/custom/images/rs-images/FPM.004.png">
              <div class="slide-content">
                <div class="slide-title">Invented in Sweden</div>
                <div class="slide-subtitle">Engineered in China</div>
                <a @click="downloadFlyer" href="javascript:void(0)" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
            <!-- SLIDE 3 -->
            <li class="slide-item" data-bg="/assets/custom/images/rs-images/FPM.005.png">
              <div class="slide-content">
                <div class="slide-title">The Reveal panels</div>
                <div class="slide-subtitle">Adding thousands of protein complexes assays to your proteomics data</div>
                <a @click="downloadFlyer" href="javascript:void(0)" class="btn btn-lg btn-wh">Learn More</a>
              </div>
            </li>
          </ul>

          <!-- 幻灯片导航按钮 -->
          <button class="slide-nav-btn slide-prev" @click="previousSlide" type="button">
            <i class="fa fa-chevron-left"></i>
          </button>
          <button class="slide-nav-btn slide-next" @click="nextSlide" type="button">
            <i class="fa fa-chevron-right"></i>
          </button>

          <!-- 幻灯片指示器 -->
          <div class="tp-bullets simplebullets round">
            <div
              v-for="(slide, index) in 3"
              :key="index"
              :class="['tp-bullet', { selected: currentSlide === index }]"
              @click="goToSlide(index)"
            ></div>
          </div>
        </div>

        <!-- Header and Menu Overlay Mode -->
        <header id="header-wrapper-mp" class="head-overlay sticky-menu-light">
          <div class="sticky-header header-dark sticky-overlay nobg">
            <div class="container mp-nav">
              <div id="site-title">
                <h1 class="font-accident-one-bold">
                  <a href="/" @click="handleTitleClick">Quantix Biosciences</a>
                </h1>
              </div>

              <div id="mobnav-btn"><i class="fa fa-bars"></i></div>

              <nav id="main-menu" class="site-navigation primary-navigation">
                <ul class="sf-menu clearfix">
                  <li>
                    <a href="#products">Products and Services</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><router-link to="/reveal2000">Reveal 2000</router-link></li>
                      <li><a href="#reveal3000">Reveal 3000 (Coming Soon)</a></li>
                    </ul>
                  </li>
                  <li>
                    <a href="#complexes">The Complexes</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><router-link to="/complexes">Protein Complexes Portfolio</router-link></li>
                      <li><a href="#subunits-showcase">Subunits connectivity showcase</a></li>
                    </ul>
                  </li>
                  <li>
                    <a href="#downloads">Downloads</a>
                    <div class="mobnav-subarrow"></div>
                    <ul class="submenu">
                      <li><a href="/downloads/COMFIDENT_flyer.pdf">Flyer for the COMFIDENT technology</a></li>
                      <li><a href="/downloads/REVEAL2000_flyer.pdf">Flyer for the Reveal 2000 panel</a></li>
                    </ul>
                  </li>
                  <!-- 用户登录入口 -->
                  <li><a @click="goToUserLogin" href="javascript:void(0)">Login</a></li>

                  <!-- 隐藏的管理员入口 - 只有在特殊情况下显示 -->
                  <li v-if="showAdminEntry" class="admin-hidden-entry">
                    <a @click="goToAdminLogin" href="javascript:void(0)" style="color: #ff6b6b; font-size: 12px;">Admin</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </header>
        <!-- /Header and Menu Overlay Mode -->
      </div>
    </article>
    <!-- /Revolution Slider -->

    <div id="content">
      <!-- Counts Block -->
      <div class="counts e-block-ins e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="count-container row">
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-paper"></div></div>
              <span class="integers digit font-accident-one-light">2,251</span>
              <div class="count-text">protein complexes analyzed</div>
            </div>
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-map"></div></div>
              <span class="integers digit font-accident-one-light">57,094</span>
              <div class="count-text">samples analyzed</div>
            </div>
            <div class="col-md-4 count wow fadeIn">
              <div class="count-icon"><div class="icon-paper-clip"></div></div>
              <span class="integers digit font-accident-one-light">3</span>
              <div class="count-text">publications submitted</div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Counts Block -->

      <!-- Download Block -->
      <div class="e-block-ins e-block-centered e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="row width-80 e-centered">
            <div class="col-md-8 text-center wow fadeIn">
              <h4 class="font-accident-one-medium">Have some questions about our products?</h4>
            </div>
            <div class="col-md-4 text-center wow fadeIn">
              <a @click="sendQuery" href="javascript:void(0)" class="btn btn-lg btn-gr">Send a query</a>
            </div>
          </div>
        </div>
      </div>
      <!-- /Download Block -->

      <!-- Details Block -->
      <div class="hp-details e-block e-block-light e-bg-light e-bg-light-texture">
        <div class="container">
          <div class="row">
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-content-right"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Integrated Approach</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    We combine proteomics detection technologies with statistical deconvolution methods, providing a unique method for analyzing protein complex abundance. This integration broadens the scope of proteomics to include complexome analysis, enabling more detailed understanding of biological systems.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-stack"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Unique Complex Fingerprint Matrix (CFM)</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our COMFIDENT technology generates a CFM by spiking synthetic protein complexes into standard human plasma samples. This matrix shows how each protein readout changes with each complex, providing a detailed profile of protein interactions within complexes.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-image"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Statistical Deconvolution</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our technique uses statistical modeling to decompose protein readouts into various complexes. This allows for the estimation of complex scores, reflecting the complexome profile in the sample.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-air-play"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">High-Throughput and Reproducibility</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    COMFIDENT addresses the challenge of quantifying protein complexes, particularly at a population scale, by using established proteomics assays. This not only simplifies the process but also ensures high throughput and reproducibility in complex score estimation.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-esc"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Wide Applicability and User-Friendliness</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    Our method is designed to be high-throughput, comprehensive, and user-friendly, making it suitable for large-scale phenotyping of protein complexes. It's adaptable for various scales and types of proteomic analysis.
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-4 infoblock">
              <div class="row">
                <div class="col-sm-2"><span class="icon-moon"></span></div>
                <div class="col-sm-10">
                  <h4 class="font-regular-normal">Facilitation of Biomarker Discovery</h4>
                  <div class="dividewhite1"></div>
                  <p>
                    COMFIDENT allows scientists to identify crucial protein complex biomarkers in low-abundance biological fluids, contributing significantly to clinical diagnosis and a deeper understanding of health and disease processes. The accumulated data from COMFIDENT enriches the field of complexome analysis, offering new insights into the proteome.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Details Block -->
    </div>

    <!-- Blog Section -->
    <div id="blog" class="e-block e-block-skin">
      <div class="container">
        <!-- Decorative arrow -->
        <div class="down-arrow">&nbsp;</div>
        <!-- /Decorative arrow -->

        <div class="dividewhite6"></div>

        <div id="primary" role="main">
          <article class="post">
            <!-- Standard Post -->
            <h3 class="post-title"><a href="#blog-post">Development of the COMFIDENT technology</a></h3>
            <div class="post-attrib">
              <div class="comment-baloon">1</div>
              <div class="post-data">
                <div class="post-date">Dec 31, 2023</div>
                <div class="post-author">by the Quantix Core Team</div>
              </div>
            </div>

            <!-- Post Content -->
            <p>
              In 2017, our team from <a href="http://ki.se">Karolinska Institutet</a> developed COMplexome FIngerprint DEconvolutioN Technology (COMFIDENT), an innovative workflow combining statistical modeling with experimental protein synthesis techniques for analyzing protein complexes. This method requires a comprehensive high-throughput proteomics assay for each specific protein complex. Following the completion of these experiments, the resultant Complex Fingerprint Matrix (CFM) enables the evaluation of high-dimensional protein complexes using advanced proteomics data.
            </p>
            <p>
              For the past five years, our team has meticulously conducted protein synthesis experiments to generate a high-quality CFM for approximately 2,000 verified protein complexes. In our initial application of this technology, we assessed 1,600 complexes in more than 30,000 individuals. The findings were remarkable, uncovering previously unknown biomarkers for complex diseases that were undetectable using existing proteomic assays.
            </p>
            <p>
              For detailed information, see our <a href="http://www.quantix.se/downloads/COMFIDENT_flyer.pdf">Flyer</a> for the introduction of COMFIDENT and our <a href="http://examples.quantix.se/showcase.pdf">Showcase</a> for the analysis results.
            </p>
            <div class="dividewhite2"></div>
            <a role="button" @click="learnMoreAboutTechnology" href="javascript:void(0)" class="btn btn-default btn-lgr-str">Learn More</a>
            <!-- /Post Content -->
          </article>

          <article class="post">
            <!-- Standard Post -->
            <h3 class="post-title"><a href="#blog-post">Advancing the proteomics field</a></h3>
            <div class="post-attrib">
              <div class="comment-baloon">2</div>
              <div class="post-data">
                <div class="post-date">Jan 7, 2024</div>
                <div class="post-author">by the Quantix Core Team</div>
              </div>
            </div>

            <!-- Post Content -->
            <p>
              The proteomics market is currently experiencing significant growth, indicating a promising potential for technologies like COMFIDENT. As of 2024, the global proteomics market is projected to reach around USD 27.60 billion and is expected to grow at a compound annual growth rate (CAGR) of 14.6%, reaching approximately USD 72.9 billion by 2028. This growth is driven by factors such as the rising demand for personalized medicine, increasing research and development expenditure, and technological advancements in the field.
            </p>
            <p>
              In terms of market segmentation, the reagents and consumables segment holds a substantial share, accounting for more than 70.30% of the global revenue in 2021. This segment is also anticipated to exhibit the fastest growth rate during the projected period. This growth can be attributed to the extensive use of reagents and consumables in various biological research settings, combined with technological developments in instruments like 2-D electrophoresis protein analyzers, which improve research efficiency.
            </p>
            <p>
              The clinical diagnostics segment dominates the proteomics industry, with a significant share of more than 51.65% in 2021. This dominance is due to the high adoption of protein analysis in clinical research for identifying disease biomarkers and risk factors, which opens new opportunities for prevention and early intervention of diseases.
            </p>
            <p>
              Given these market dynamics, the COMFIDENT technology, with its focus on high-dimensional protein complex analysis, could be well-positioned to capitalize on the growing proteomics market. Its potential to uncover novel biomarkers for complex diseases aligns with the current market trend towards personalized medicine and clinical diagnostics. Furthermore, the demand for advanced and efficient diagnostic tools and drug discovery platforms is on the rise, which could further justify the potential profitability and market size for COMFIDENT.
            </p>
            <div class="dividewhite2"></div>
            <a role="button" @click="learnMoreAboutMarket" href="javascript:void(0)" class="btn btn-default btn-lgr-str">Learn More</a>
            <!-- /Post Content -->
          </article>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <AppFooter />
    <!-- /Footer -->
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import AppFooter from '@/components/AppFooter.vue'

export default defineComponent({
  name: 'HomeLandingView',
  components: {
    AppFooter
  },
  setup() {
    const router = useRouter()
    const isLoading = ref(true)
    const currentSlide = ref(0)
    const slideInterval = ref<NodeJS.Timeout | null>(null)
    const showAdminEntry = ref(false)
    const adminClickCount = ref(0)

    const goToUserLogin = () => {
      router.push('/login?type=user')
    }

    const goToAdminLogin = () => {
      router.push('/login?type=admin')
    }

    const handleTitleClick = (event: Event) => {
      event.preventDefault()
      adminClickCount.value++

      // 连续点击5次标题显示管理员入口
      if (adminClickCount.value >= 5) {
        showAdminEntry.value = true
        // 10秒后自动隐藏
        setTimeout(() => {
          showAdminEntry.value = false
          adminClickCount.value = 0
        }, 10000)
      }

      // 重置计数器（如果2秒内没有继续点击）
      setTimeout(() => {
        if (adminClickCount.value < 5) {
          adminClickCount.value = 0
        }
      }, 2000)
    }

    const downloadFlyer = () => {
      // 创建一个临时的下载链接
      const link = document.createElement('a')
      link.href = '/downloads/COMFIDENT_flyer.pdf'
      link.download = 'COMFIDENT_flyer.pdf'
      link.target = '_blank'

      // 如果文件不存在，显示提示信息
      fetch(link.href, { method: 'HEAD' })
        .then(response => {
          if (response.ok) {
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          } else {
            alert('PDF文件正在准备中，请稍后再试或联系我们获取更多信息。')
          }
        })
        .catch(() => {
          alert('PDF文件正在准备中，请稍后再试或联系我们获取更多信息。')
        })
    }

    const learnMoreAboutTechnology = () => {
      // 滚动到技术详情部分或显示更多信息
      const detailsSection = document.querySelector('.details-block')
      if (detailsSection) {
        detailsSection.scrollIntoView({ behavior: 'smooth' })
      }
    }

    const learnMoreAboutMarket = () => {
      // 滚动到联系部分
      const contactSection = document.querySelector('.contact-block')
      if (contactSection) {
        contactSection.scrollIntoView({ behavior: 'smooth' })
      } else {
        // 如果没有联系部分，显示联系信息
        alert('如需了解更多市场信息，请联系我们：<EMAIL>')
      }
    }

    const sendQuery = () => {
      // 创建邮件链接
      const subject = encodeURIComponent('Product Inquiry - COMFIDENT Technology')
      const body = encodeURIComponent(`Dear Quantix Biosciences Team,

I am interested in learning more about your COMFIDENT technology and Reveal panels. Please provide me with additional information about:

- Product specifications and capabilities
- Pricing and availability
- Technical support and training
- Partnership opportunities

Thank you for your time and I look forward to hearing from you.

Best regards,`)

      const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`

      // 尝试打开邮件客户端
      try {
        window.location.href = mailtoLink
      } catch (error) {
        // 如果无法打开邮件客户端，显示联系信息
        alert(`请通过以下方式联系我们：\n\n邮箱：<EMAIL>\n电话：+46 70 475 1821\n\n或者您可以复制邮箱地址手动发送邮件。`)
      }
    }

    const goToSlide = (index: number) => {
      const slides = document.querySelectorAll('.slide-item')
      if (slides.length > 0) {
        slides[currentSlide.value].classList.remove('active')
        currentSlide.value = index
        slides[currentSlide.value].classList.add('active')

        // Update background
        const newBg = slides[currentSlide.value].getAttribute('data-bg')
        if (newBg) {
          const banner = document.querySelector('.tp-banner')
          if (banner) {
            (banner as HTMLElement).style.backgroundImage = `url(${newBg})`
          }
        }
      }
    }

    const nextSlide = () => {
      const slides = document.querySelectorAll('.slide-item')
      const nextIndex = (currentSlide.value + 1) % slides.length
      goToSlide(nextIndex)
    }

    const previousSlide = () => {
      const slides = document.querySelectorAll('.slide-item')
      const prevIndex = currentSlide.value === 0 ? slides.length - 1 : currentSlide.value - 1
      goToSlide(prevIndex)
    }

    const startSlideshow = () => {
      slideInterval.value = setInterval(() => {
        nextSlide()
      }, 5000) // Change slide every 5 seconds
    }

    const stopSlideshow = () => {
      if (slideInterval.value) {
        clearInterval(slideInterval.value)
        slideInterval.value = null
      }
    }

    onMounted(() => {
      // Hide loader after a short delay
      setTimeout(() => {
        isLoading.value = false
      }, 1000)

      // Start slideshow after loader is hidden
      setTimeout(() => {
        startSlideshow()
      }, 1500)

      // Load external scripts and styles
      const loadExternalResources = () => {
        // Load Bootstrap CSS
        const bootstrapCSS = document.createElement('link')
        bootstrapCSS.rel = 'stylesheet'
        bootstrapCSS.href = '/assets/vendor/bootstrap/css/bootstrap.min.css'
        document.head.appendChild(bootstrapCSS)

        // Load FontAwesome CSS
        const fontAwesomeCSS = document.createElement('link')
        fontAwesomeCSS.rel = 'stylesheet'
        fontAwesomeCSS.href = '/assets/vendor/fontawesome/css/font-awesome.css'
        document.head.appendChild(fontAwesomeCSS)

        // Load custom CSS
        const customCSS = document.createElement('link')
        customCSS.rel = 'stylesheet'
        customCSS.href = '/assets/custom/css/style.css'
        document.head.appendChild(customCSS)

        // Load jQuery
        const jqueryScript = document.createElement('script')
        jqueryScript.src = '/assets/vendor/jquery/js/jquery-1.10.1.min.js'
        document.head.appendChild(jqueryScript)

        // Load Bootstrap JS
        jqueryScript.onload = () => {
          const bootstrapScript = document.createElement('script')
          bootstrapScript.src = '/assets/vendor/bootstrap/js/bootstrap.min.js'
          document.head.appendChild(bootstrapScript)
        }
      }

      loadExternalResources()
    })

    onUnmounted(() => {
      stopSlideshow()
    })

    return {
      isLoading,
      currentSlide,
      goToUserLogin,
      goToAdminLogin,
      handleTitleClick,
      showAdminEntry,
      downloadFlyer,
      learnMoreAboutTechnology,
      learnMoreAboutMarket,
      sendQuery,
      goToSlide,
      nextSlide,
      previousSlide
    }
  },
})
</script>

<style scoped>

/* 导航样式修复 */
.sf-menu ul li {
  background: #333 !important;
  opacity: 1 !important;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.2);
}

.sf-menu ul li:hover,
.sf-menu ul li.sfHover {
  background: #333 !important;
  opacity: 1 !important;
}

.sf-menu ul li ul li {
  background: #333 !important;
  opacity: 1 !important;
}

.sf-menu ul li ul li:hover,
.sf-menu ul li ul li.sfHover {
  background: #333 !important;
  opacity: 1 !important;
}

.sf-menu ul {
  background: #333 !important;
  opacity: 1 !important;
}

.submenu {
  background: #333 !important;
  opacity: 1 !important;
}

.submenu a {
  color: #e8e8e8 !important;
}

.submenu li:hover > a {
  color: #fff !important;
}

/* 管理员隐藏入口样式 */
.admin-hidden-entry {
  opacity: 0.7;
}

.admin-hidden-entry:hover {
  opacity: 1;
}

/* 幻灯片导航按钮样式 */
.slide-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: white;
  font-size: 20px;
  pointer-events: auto !important;
}

.slide-prev {
  left: 30px;
}

.slide-next {
  right: 30px;
}

.slide-nav-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.slide-nav-btn:focus {
  outline: none;
}

.slide-nav-btn:active {
  transform: translateY(-50%) scale(0.95);
}

/* 幻灯片指示器样式 */
.tp-bullets {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10000 !important;
  pointer-events: auto !important;
}

.tp-bullet {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto !important;
  z-index: 10001 !important;
}

.tp-bullet.selected,
.tp-bullet:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.2);
}



.quantix-homepage {
  font-family: 'Arial', sans-serif;
  color: #333;
  background-color: #fff;
  position: relative;
  min-height: 100vh;
}

/* Loader Styles */
#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: #fff;
}

#loader {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-section {
  position: fixed;
  top: 0;
  width: 51%;
  height: 100%;
  background: #fff;
  z-index: 1000;
  transform: translateX(0);
}

.loader-section.section-left {
  left: 0;
}

.loader-section.section-right {
  right: 0;
}

/* Slider Styles */
.slider-revolution {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.tp-banner-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.tp-banner {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('/assets/custom/images/rs-images/FPM.003.png');
  transition: background-image 1s ease-in-out;
}

.tp-banner ul {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}

.slide-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slide-item.active {
  opacity: 1;
}

.slide-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 20px;
}

.slide-title {
  font-size: 56px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-subtitle {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Header Styles */
#header-wrapper-mp {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
}

.sticky-header {
  background: transparent;
  padding: 15px 0;
}

.container.mp-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  min-height: 80px;
}

#site-title {
  float: left;
  display: flex;
  align-items: center;
}

#site-title h1 {
  margin: 0;
  font-size: 28px;
  text-transform: uppercase;
}

#site-title h1 a {
  color: white;
  text-decoration: none;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

#main-menu {
  float: right;
  border: none;
  display: flex;
  align-items: center;
}

#main-menu > ul {
  list-style: none;
  margin: 0;
  display: flex;
  align-items: center;
}

/* 确保下拉菜单保持垂直排列 */
#main-menu ul ul {
  display: block !important;
  flex-direction: column !important;
}

#main-menu > li {
  display: inline-block;
  margin-right: 24px;
  border: none;
}

#main-menu li:last-child {
  margin-right: 0;
}

#main-menu a {
  font-weight: 400;
  font-size: 13px;
}

#main-menu > ul > li > a {
  text-transform: uppercase;
  font-weight: 700;
  font-size: 13px;
}

/* 强制覆盖全局CSS的margin-top设置，确保垂直居中 */
#header-wrapper-mp #site-title h1 {
  margin-top: 0 !important;
}

#header-wrapper-mp #main-menu {
  margin-top: 0 !important;
}

/* 确保sticky状态下也保持居中 */
#header-wrapper-mp .is-sticky #site-title h1 {
  margin-top: 0 !important;
}

#header-wrapper-mp .is-sticky #main-menu {
  margin-top: 0 !important;
}

#mobnav-btn {
  display: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.site-navigation ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  gap: 30px;
}

.site-navigation ul li {
  position: relative;
}

.site-navigation ul li a {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.site-navigation ul li a:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.site-navigation ul li.admin-access a {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.site-navigation ul li.admin-access a:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Submenu Styles */
.submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  min-width: 200px;
  list-style: none;
  margin: 0;
  padding: 10px 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border-radius: 5px;
}

.site-navigation ul li:hover .submenu {
  opacity: 1;
  visibility: visible;
}

.submenu li {
  width: 100%;
}

.submenu li a {
  display: block;
  padding: 10px 20px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.submenu li a:hover,
.submenu li router-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Router Link Styles */
.submenu router-link {
  display: block;
  padding: 10px 20px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 12px 30px;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s ease;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  border: none;
}

.btn-wh {
  background-color: white;
  color: #333;
}

.btn-wh:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.btn-gr {
  background-color: #28a745;
  color: white;
}

.btn-gr:hover {
  background-color: #218838;
}

.btn-lg {
  padding: 15px 35px;
  font-size: 18px;
}

/* Content Styles */
#content {
  position: relative;
  z-index: 1;
}

/* Counts Block */
.counts {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.count-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
}

.count {
  text-align: center;
  padding: 20px;
  margin: 20px 0;
}

.count-icon {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 20px;
}

.count .digit {
  font-size: 48px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.count-text {
  font-size: 16px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Footer */
#footer {
  background-color: #333;
  color: white;
  padding: 60px 0 40px;
}

#footer .infoblock {
  margin-bottom: 30px;
}

#footer .infoblock h2 {
  font-size: 18px;
  margin-bottom: 20px;
  color: white;
}

#footer .infoblock p {
  font-size: 14px;
  line-height: 1.6;
  color: #ccc;
}

.contact .footer-addr {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.footer-icon {
  width: 30px;
  color: #667eea;
  margin-right: 10px;
}

.addr-text {
  font-size: 14px;
  color: #ccc;
}

.addr-text a {
  color: #667eea;
  text-decoration: none;
}

.addr-text a:hover {
  text-decoration: underline;
}

.follow-element {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.follow-icon {
  width: 30px;
  color: #667eea;
  margin-right: 10px;
}

.follow-descr {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.follow-social a {
  color: #667eea;
  text-decoration: none;
}

.follow-social a:hover {
  text-decoration: underline;
}

.follow-numb {
  color: #ccc;
  font-size: 12px;
}

/* Copyright */
#copyrights-wrapper {
  background-color: #222;
  padding: 20px 0;
}

.copyright {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.copy-attrs a,
.copy-link a {
  color: #667eea;
  text-decoration: none;
}

.copy-attrs a:hover,
.copy-link a:hover {
  text-decoration: underline;
}

/* Back to Top */
#back-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

#back-top a {
  display: block;
  width: 50px;
  height: 50px;
  background-color: #667eea;
  color: white;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
}

#back-top a:hover {
  background-color: #5a6fd8;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  #mobnav-btn {
    display: block;
  }

  .site-navigation {
    display: none;
  }

  .slide-title {
    font-size: 36px;
  }

  .slide-subtitle {
    font-size: 18px;
  }

  .count-container {
    flex-direction: column;
  }

  .post {
    padding: 20px;
  }

  .copyright {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  #back-top {
    bottom: 20px;
    right: 20px;
  }

  #back-top a {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
}

/* Animation Classes */
.wow {
  visibility: hidden;
}

.wow.fadeIn {
  animation: fadeIn 1s ease-in-out;
  visibility: visible;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Font Classes */
.font-accident-one-bold {
  font-weight: bold;
}

.font-accident-one-light {
  font-weight: 300;
}

.font-accident-one-medium {
  font-weight: 500;
}

.font-regular-normal {
  font-weight: normal;
}

/* Icon Classes */
.icon-paper::before { content: "📄"; }
.icon-map::before { content: "🗺️"; }
.icon-paper-clip::before { content: "📎"; }
.icon-content-right::before { content: "📊"; }
.icon-stack::before { content: "📚"; }
.icon-image::before { content: "🖼️"; }
.icon-air-play::before { content: "📡"; }
.icon-esc::before { content: "⚡"; }
.icon-moon::before { content: "🌙"; }

</style>