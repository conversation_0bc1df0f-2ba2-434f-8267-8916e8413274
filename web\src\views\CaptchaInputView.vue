<template>
  <div class="captcha-container">
    <div class="captcha-card">
      <h2>请输入验证码</h2>
      <p v-if="email">我们已将验证码发送到 {{ email }}，请查收。</p>
      <p v-else>我们已将验证码发送到您的邮箱，请查收。</p>

      <form @submit.prevent="handleCaptchaSubmit">
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>

        <div class="form-group">
          <label for="email">邮箱:</label>
          <input
            type="email"
            id="email"
            v-model="email"
            :disabled="loading"
            required
          />
        </div>

        <div class="form-group">
          <label for="captchaCode">验证码:</label>
          <input
            type="text"
            id="captchaCode"
            v-model="captchaCode"
            :disabled="loading"
            maxlength="6"
            placeholder="请输入6位验证码"
            required
          />
        </div>

        <button type="submit" class="btn-primary" :disabled="loading">
          <span v-if="loading">{{ '验证中...' }}</span>
          <span v-else>提交验证</span>
        </button>
      </form>

      <p class="resend-link">
        <a href="#" @click.prevent="resendCaptcha" :class="{ disabled: resendLoading || countdown > 0 }">
          <span v-if="resendLoading">发送中...</span>
          <span v-else-if="countdown > 0">重新发送 ({{ countdown }}s)</span>
          <span v-else>重新发送验证码</span>
        </a>
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/counter';
import { authAPI } from '@/services/apiService';

export default defineComponent({
  name: 'CaptchaInputView',
  setup() {
    const captchaCode = ref('');
    const email = ref('');
    const router = useRouter();
    const userStore = useUserStore();
    const loading = ref(false);
    const resendLoading = ref(false);
    const errorMessage = ref('');
    const successMessage = ref('');
    const countdown = ref(0);
    let countdownTimer: number | null = null;

    onMounted(() => {
      // 从本地存储获取待验证的邮箱
      const pendingEmail = localStorage.getItem('pendingEmail');
      if (pendingEmail) {
        email.value = pendingEmail;
      } else {
        // 如果没有待验证邮箱，跳转到注册页面
        router.push('/register');
      }
    });

    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    });

    const startCountdown = () => {
      countdown.value = 60;
      countdownTimer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(countdownTimer!);
          countdownTimer = null;
        }
      }, 1000);
    };

    const handleCaptchaSubmit = async () => {
      if (!email.value || !captchaCode.value) {
        errorMessage.value = '请填写邮箱和验证码';
        return;
      }

      if (captchaCode.value.length !== 6) {
        errorMessage.value = '验证码必须是6位数字';
        return;
      }

      loading.value = true;
      errorMessage.value = '';
      successMessage.value = '';

      try {
        const result = await userStore.verifyEmail({
          email: email.value,
          verificationCode: captchaCode.value
        });

        if (result.success) {
          successMessage.value = '邮箱验证成功！';
          localStorage.removeItem('pendingEmail');
          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        } else {
          errorMessage.value = result.error || '验证码错误';
        }
      } catch (error) {
        errorMessage.value = '网络错误，请稍后重试';
      } finally {
        loading.value = false;
      }
    };

    const resendCaptcha = async () => {
      if (resendLoading.value || countdown.value > 0) {
        return;
      }

      if (!email.value) {
        errorMessage.value = '请输入邮箱地址';
        return;
      }

      resendLoading.value = true;
      errorMessage.value = '';

      try {
        const response = await authAPI.resendVerification({ email: email.value });
        successMessage.value = '验证码已重新发送';
        startCountdown();
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || '发送失败，请稍后重试';
      } finally {
        resendLoading.value = false;
      }
    };

    return {
      captchaCode,
      email,
      loading,
      resendLoading,
      errorMessage,
      successMessage,
      countdown,
      handleCaptchaSubmit,
      resendCaptcha,
    };
  },
});
</script>

<style scoped>
.captcha-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(to right, #ece9e6, #ffffff); /* 渐变背景 */
  font-family: 'Arial', sans-serif;
}

.captcha-card {
  background-color: #fff;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
  max-width: 400px;
}

h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 28px;
}

p {
  color: #666;
  margin-bottom: 25px;
  font-size: 16px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: bold;
}

.form-group input {
  width: calc(100% - 20px);
  padding: 12px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.btn-primary {
  background-color: #28a745;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.btn-primary:hover {
  background-color: #218838;
}

.resend-link {
  margin-top: 20px;
  color: #777;
}

.resend-link a {
  color: #007bff;
  text-decoration: none;
  font-weight: bold;
}

.resend-link a:hover {
  text-decoration: underline;
}

.resend-link a.disabled {
  color: #ccc;
  cursor: not-allowed;
  pointer-events: none;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.btn-primary:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}
</style>