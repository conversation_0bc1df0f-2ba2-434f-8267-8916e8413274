
/*------------------------------------------------------------------
[Table of contents]

1. Color Sheme / Colors, textures
2. Blocks
3. Typography / Heading, Regular
4. Basic Styles
5. <PERSON><PERSON>
6. <PERSON><PERSON>
7. Drop-down menu
8. Main Page
9. Inner Pages
10. Hover Effects
11. Google Map
12. Footer Styles
13. Media Query]


-------------------------------------------------------------------*/







/*------------------------------------------------------------------
[1. Color Sheme / Colors, textures]
*/


/* White Skin */
$bg-skin: #fff;

/* Contrast */
$bg-light:  #e8e8e8;
$bg-medium: #666;
$bg-dark:   #333;
$bg-darker: #2b2b2b;
$bg-accent: #586682;
$bg-light-texture: url('../images/backgrounds/contrast/bg-light.png');
$bg-dark-texture:  url('../images/backgrounds/contrast/bg-dark.png');
$fontcolor-regular: #333;
$fontcolor-medium: #999;
$fontcolor-invert: #fff;
$fontcolor-medium-light: #f2f2f2;
$hovercolor: #de3627;
$accentcolor: #ff9900;

/* Blue-grey */
//$bg-light: #e8f3f8;
//$bg-medium: #2a5a72;
//$bg-dark:   #2F4550;
//$bg-darker: #2a3e48;
//$bg-accent: #007EA7;
//$bg-light-texture: url('../images/backgrounds/blue-grey/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/blue-grey/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #E84855;
//$accentcolor: #ff9900;

/* Cyan */
//$bg-light:  #e8f1f4;
//$bg-medium: #255d73;
//$bg-dark: #194759;
//$bg-darker: #183f4e;
//$bg-accent: #CC4E5C;
//$bg-light-texture: url('../images/backgrounds/cyan/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/cyan/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #CC4E5C;
//$accentcolor: #D1626F;

/* Brown */
//$bg-light:  #e8d6d1;
//$bg-medium: #7C5B59;
//$bg-dark: #6e4d44;
//$bg-darker: #62433b;
//$bg-accent: #D7AF70;
//$bg-light-texture: url('../images/backgrounds/brown/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/brown/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #EA526F;
//$accentcolor: #F5CB5C;

/* Deep Orange */
//$bg-light:  #ffe9da;
//$bg-medium: #d94900;
//$bg-dark: #c34200;
//$bg-darker: #b83e00;
//$bg-accent: #00a0b0;
//$bg-light-texture: url('../images/backgrounds/orange/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/orange/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #0C5C7A;
//$accentcolor: #00a0b0;

/* Miami */
//$bg-light:  #F2E6E6;
//$bg-medium: #9e6898;
//$bg-dark:   #703851;
//$bg-darker: #663149;
//$bg-accent: #65C6A4;
//$bg-light-texture: url('../images/backgrounds/miami/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/miami/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #9B2045;
//$accentcolor: #90ED84;

/* Times */
//$bg-light:  #f4e7f4;
//$bg-medium: #986296;
//$bg-dark: #5f435e;
//$bg-darker: #513a50;
//$bg-accent: #BF837E;
//$bg-light-texture: url('../images/backgrounds/times/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/times/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #961D4E;
//$accentcolor: #B26D67;

/* Camp */
//$bg-light:  #f4e6e5;
//$bg-medium: #D96459;
//$bg-dark:   #954d4d;
//$bg-darker: #8C4646;
//$bg-accent: #83B692;
//$bg-light-texture: url('../images/backgrounds/camp/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/camp/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #F9627D;
//$accentcolor: #F9ADA0;

/* Pink */
//$bg-light:  #fae8ea;
//$bg-medium: #F57A82;
//$bg-dark:   #ED5276;
//$bg-darker: #e04065;
//$bg-accent: #FF7751;
//$bg-light-texture: url('../images/backgrounds/pink/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/pink/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #D90558;
//$accentcolor: #FF7751;

/* Grass */
//$bg-light:  #F3F9D2;
//$bg-medium: #788451;
//$bg-dark:   #646f41;
//$bg-darker: #5c673b;
//$bg-accent: #EC7357;
//$bg-light-texture: url('../images/backgrounds/grass/bg-light.png');
//$bg-dark-texture:  url('../images/backgrounds/grass/bg-dark.png');
//$fontcolor-regular: #333;
//$fontcolor-medium: #999;
//$fontcolor-invert: #fff;
//$fontcolor-medium-light: #f2f2f2;
//$hovercolor: #703851;
//$accentcolor: #9B2045;


// Color Classes
.fontcolor-regular, .fontcolor-regular a {color: $fontcolor-regular;}
.fontcolor-invert, .fontcolor-invert a {color: $fontcolor-invert;}
.fontcolor-medium-light, .fontcolor-medium-light a {color: $fontcolor-medium-light;}
.hovercolor, .hovercolor a {color: $hovercolor;}

.accent-color, .accent-color a {color: $bg-accent !important;}
.accent-color a:hover {color: $hovercolor !important;}



/** Backgrounds **/
//$bg-light-texture: url('../images/bg-light.png') center 0 no-repeat;
//$bg-dark-texture:  url('../images/bg-dark.png') center 0 no-repeat;






/*------------------------------------------------------------------
[2. Blocks]
*/


@mixin block-margins {  padding: 100px 0 100px 0;  }
@mixin block-ins-margins {  padding: 70px 0 70px 0;  }
@mixin infoblock-margins {  margin: 0 0 35px 0;  }

.e-block {  @include block-margins;  }
.e-block-ins {@include block-ins-margins;}
.e-block-skin {  background: $bg-skin;  }
.e-block-light {
  background: $bg-light;
  color: $fontcolor-regular;
  p, h2, h3, h4, h5 {
    color: $fontcolor-regular;
  }
  a, .infoblock a {
    color: $fontcolor-regular;
    &:hover {
      color: $hovercolor;
      text-decoration: none;
    }
  }
}

.e-block-dark {
  background: $bg-dark;
  color: $fontcolor-invert;
  p, h2, h3, h4, h5 {
    color: $fontcolor-medium-light;
  }
  a, .infoblock a {
	color: $fontcolor-invert;
	&:hover {
	  color: $hovercolor;
	  text-decoration: none;
	}
  }
}

.e-block-assent {
  background: $bg-accent;
  color: $fontcolor-invert;
  p, h2, h3, h4, h5 {
    color: $fontcolor-invert;
  }
  a, .infoblock a {
    color: $fontcolor-invert;
    &:hover {
      color: $hovercolor;
      text-decoration: none;
    }
  }
}

.e-block-centered {  text-align: center;  margin: 0 auto;  }

.border-bot-3 {  border-bottom: 3px solid $bg-dark;  }

.border-top-3 {  border-top: 3px solid $bg-dark;  }

.texture-overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-image: url(../images/grid.png);
}


@mixin clearfix() {
  &:before,
  &:after {
    content: "";
    display: table;
  }
  &:after {
    clear: both;
  }
}


/** Background Classes **/

.e-bg-skin {background: $bg-skin;}
.e-bg-light {  background: $bg-light;  }
.e-bg-light-texture {background: $bg-light-texture;}
.e-bg-medium {background: $bg-medium;}
.e-bg-dark {  background: $bg-dark; color: $fontcolor-invert; }
.e-bg-dark-texture {background: $bg-dark-texture;}

.e-bg-accent { background: $bg-accent; color: $fontcolor-invert;}

.e-bg-section {  }


.e-bg-section01 {
  @include clearfix;
  overflow: hidden; // added for pseudo-element
  position: relative; // added for pseudo-element
  &::before {
    content: ' ';
    position: fixed; // instead of background-attachment
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $bg-light;
    background: url("../images/section-bg01.jpg") no-repeat center center;
    background-size: cover;
    will-change: transform; // creates a new paint layer
    z-index: -1;
  }
}
.e-bg-section02 {
  @include clearfix;
  overflow: hidden !important; // added for pseudo-element
  position: relative; // added for pseudo-element
  &::before {
    content: ' ';
    position: fixed; // instead of background-attachment
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $bg-light;
    background: url("../images/section-bg02.jpg") no-repeat center center;
    background-size: cover;
    will-change: transform; // creates a new paint layer
    z-index: -1;
  }
}
.e-bg-section03 {
  @include clearfix;
  overflow: hidden; // added for pseudo-element
  position: relative; // added for pseudo-element
  &::before {
    content: ' ';
    position: fixed; // instead of background-attachment
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $bg-light;
    background: url("../images/section-bg03.jpg") no-repeat center center;
    background-size: cover;
    will-change: transform; // creates a new paint layer
    z-index: -1;
  }
}
.e-bg-section04 {
  @include clearfix;
  overflow: hidden; // added for pseudo-element
  position: relative; // added for pseudo-element
  &::before {
    content: ' ';
    position: fixed; // instead of background-attachment
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $bg-light;
    background: url("../images/section-bg04.jpg") no-repeat center center;
    background-size: cover;
    will-change: transform; // creates a new paint layer
    z-index: -1;
  }
}
.e-bg-section05 {
  @include clearfix;
  overflow: hidden; // added for pseudo-element
  position: relative; // added for pseudo-element
  &::before {
    content: ' ';
    position: fixed; // instead of background-attachment
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $bg-light;
    background: url("../images/section-bg05.jpg") no-repeat center center;
    background-size: cover;
    will-change: transform; // creates a new paint layer
    z-index: -1;
  }
}


.boxed-hero {
  &::before {
    max-width: 1300px !important;
    margin-right: auto;
    margin-left: auto;
    left: auto !important;
  }
}




.heroimage-wrap {
//  position: relative;
//  top: 0;
//  width: 100%;
//  background-position: top center;
//  background-repeat: no-repeat;
}
.hero-text-wrap {
  width: 100%;
  position: absolute;
  top: 40%;
  text-transform: uppercase;
  text-align: center;
}
.hero-text-wrap ul {
  list-style: none;
  display: inline-block;
  width: 100%;
}
.hero-text-wrap ul li {
  display: none;
  padding: 0 20px;
}
.hero-text-wrap ul.single li:first-child {
  display: block;
}
.hero-text-wrap ul.border li {
  border: 1px solid #fff;
  padding: 20px 40px 10px;
}
.hero-text-wrap ul.border-top-bottom li {
  border-top: 1px solid rgba(255,255,255,0.2);
  border-bottom: 1px solid rgba(255,255,255,0.2);
  padding: 20px 40px 10px;
}
.hero-text-wrap .hero-subtitle {
  color: #ffffff;
  color: rgba(255,255,255,0.9);
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 4px;
  line-height: 12px;
  margin-bottom: 30px;
}
.hero-text-wrap .hero-title {
  font-size:70px;
  letter-spacing: 8px;
  line-height: 90px;
  color:#fff;
  font-weight: 800;
}



/*------------------------------------------------------------------
[3. Typography]
*/


/* Headings Fonts variables */

$font-accident-one-book:          Novecentosanswide-Book,      sans-serif;
$font-accident-one-bold:          Novecentosanswide-Bold,      sans-serif;
$font-accident-one-demibold:      Novecentosanswide-DemiBold,  sans-serif;
$font-accident-one-light:         Novecentosanswide-Light,     sans-serif;
$font-accident-one-medium:        Novecentosanswide-Medium,    sans-serif;
$font-accident-one-normal:        Novecentosanswide-Normal,    sans-serif;
$font-accident-two: 'Georgia',                  serif;
$font-regular: 'Oxygen',                        sans-serif;


/* Heading font one */
.font-accident-one-bold           {  font-family: $font-accident-one-bold;  font-weight: normal;  font-style: normal;  }
.font-accident-one-book           {  font-family: $font-accident-one-book;  font-weight: normal;  font-style: normal;  }
.font-accident-one-demibold       {  font-family: $font-accident-one-demibold;  font-weight: normal;  font-style: normal;  }
.font-accident-one-light          {  font-family: $font-accident-one-light;  font-weight: normal;  font-style: normal;  }
.font-accident-one-medium         {  font-family: $font-accident-one-medium;  font-weight: normal;  font-style: normal;  }
.font-accident-one-normal         {  font-family: $font-accident-one-normal;  font-weight: normal;  font-style: normal;  }


/* Heading font two */
.font-accident-two-normal         {  font-family: $font-accident-two;  font-style: normal;  }
.font-accident-two-oblique        {  font-family: $font-accident-two;  font-style: oblique;  }
.font-accident-two-bold           {  font-family: $font-accident-two;  font-weight: bold ;  }
.font-accident-two-bold-italic    {  font-family: $font-accident-two;  font-weight: bold;  font-style: oblique;  }


/* Regular font */
.font-regular-light               {  font-family: $font-regular;  font-weight: 400;  font-style: normal;  }
.font-regular-normal              {  font-family: $font-regular;  font-weight: 400;  font-style: normal;  }
.font-regular-bold                {  font-family: $font-regular;  font-weight: 700;  font-style: normal;  }


/* Headings */
h1 {font-family: $font-accident-one-light; font-size: 60px; font-weight: normal; font-style: normal;}
h2 {font-family: $font-accident-one-normal; font-size: 36px; font-weight: normal; font-style: normal;}
h3 {font-family: $font-accident-one-medium; font-size: 28px; font-weight: normal; font-style: normal;}
h4 {font-family: $font-accident-one-medium; font-size: 24px; font-weight: normal; font-style: normal;}
h5 {font-family: $font-accident-one-medium; font-size: 21px; font-weight: normal; font-style: normal;}
h6 {font-family: $font-accident-one-bold; font-size: 16px; font-weight: normal; font-style: normal;}


.superheading {  font-size: 48px;  }


/*  ----------------------------------------------------------------
Preloader
*/

#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 19000;
}
#loader {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #3498db;
  -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
  z-index: 1001;
}
#loader:before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #e74c3c;
  -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
}
#loader:after {
  content: "";
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #f9c922;
  -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
}
@-webkit-keyframes spin {
  0%   {
    -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
    -ms-transform: rotate(0deg);  /* IE 9 */
    transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
    -ms-transform: rotate(360deg);  /* IE 9 */
    transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
  }
}
@keyframes spin {
  0%   {
    -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
    -ms-transform: rotate(0deg);  /* IE 9 */
    transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
    -ms-transform: rotate(360deg);  /* IE 9 */
    transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
  }
}
#loader-wrapper .loader-section {
  position: fixed;
  top: 0;
  width: 51%;
  height: 100%;
  background: $fontcolor-invert;
  z-index: 1000;
  -webkit-transform: translateX(0);  /* Chrome, Opera 15+, Safari 3.1+ */
  -ms-transform: translateX(0);  /* IE 9 */
  transform: translateX(0);  /* Firefox 16+, IE 10+, Opera */
}
#loader-wrapper .loader-section.section-left {  left: 0;  }
#loader-wrapper .loader-section.section-right {  right: 0;  }

/* Loaded */
.loaded #loader-wrapper .loader-section.section-left {
  -webkit-transform: translateX(-100%);  /* Chrome, Opera 15+, Safari 3.1+ */
  -ms-transform: translateX(-100%);  /* IE 9 */
  transform: translateX(-100%);  /* Firefox 16+, IE 10+, Opera */
  -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
  transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
}
.loaded #loader-wrapper .loader-section.section-right {
  -webkit-transform: translateX(100%);  /* Chrome, Opera 15+, Safari 3.1+ */
  -ms-transform: translateX(100%);  /* IE 9 */
  transform: translateX(100%);  /* Firefox 16+, IE 10+, Opera */
  -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
  transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
}
.loaded #loader {
  opacity: 0;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.loaded #loader-wrapper {
  visibility: hidden;
  -webkit-transform: translateY(-100%);  /* Chrome, Opera 15+, Safari 3.1+ */
  -ms-transform: translateY(-100%);  /* IE 9 */
  transform: translateY(-100%);  /* Firefox 16+, IE 10+, Opera */
  -webkit-transition: all 0.3s 1s ease-out;
  transition: all 0.3s 1s ease-out;
}



/*------------------------------------------------------------------
[4. Basic Styles]
*/

// Body
body {
  display: block;
  overflow-x: hidden;
  overflow-y: auto;
  color: $fontcolor-regular;
  font: 14px/1.7 $font-regular;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility:    hidden;
  -ms-backface-visibility:     hidden;
//    background: url("");
}
.boxed {
  display: block;
  background-color: #676767;
  background-image: url('../images/patterns/tileable_wood_texture.png');
  position: relative;
  max-width: 1300px;
  margin-right: auto;
  margin-left: auto;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 40px 4px;
}

// Unordered list
ul {
  list-style: none;
  margin-left: 0px;
  li { padding: 4px 0; }
}
ul.list-o {
  list-style: none;
  padding:0;
  margin:0;
  li {
    padding-left: 1em;
    text-indent: -.7em;
    &:before {
      padding-right: 4px;
      content: "* ";
      color: $hovercolor;
    }
  }
}

// No Padding elements
.row-no-padding {
  [class*="col-"] {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
.nopadding {  padding: 0 !important;  margin: 0 !important;  }


/* Dividers */
.dividewhite1 {  height: 10px;  }
.dividewhite2 {  height: 20px;  }
.dividewhite3 {  height: 30px;  }
.dividewhite4 {  height: 40px;  }
.dividewhite6 {  height: 60px;  }
.dividewhite8 {  height: 80px;  }
.dividewhite9 {  height: 90px;  }
.dividewhite10 {  height: 100px;  }


/* Misc */

// Back to top
#back-top a {
  background: #ccc url(../images/back_top.png) no-repeat 50% 50%;
  bottom: 60px;
  display: block;
  height: 40px;
  position: fixed;
  border-radius: 2px;
  right: 30px;
  width: 40px;
  z-index: 10;
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
  &:hover {  background: $hovercolor url(../images/back_top.png) no-repeat 50% 50%;  }
}

// Display
.inline { display: inline;}
.block {  display: block;  }
.inline-block {  display: inline-block;  }
.e-centered {margin: 0 auto;}

// Text
.uppercase {text-transform: uppercase;}

//
i {  color: $fontcolor-medium-light;  }

.hidden {  display: none;  }

.respon-wrap img {
  max-width:100% !important;
  height:auto;
  display:block;
}

//Color Classes
.white      {  color: #fff;}
.color-333  {  color: #333;  }
.color-666  {  color: #666666;  }
.color-999  {  color: #999;  }
.fullwidth  {  width: 100% !important;  }
.width-90   {  width: 90%;  }
.width-80   {  width: 80%;  }
.width-70   {  width: 70%;  }
.width-60   {  width: 60%;  }
.width-50   {  width: 50%;  }



/* Bootstrap tabs */
.nav-tabs {
  border-bottom: none;
  li {
    padding: 0;
    font-size: 14px;
    font-weight: 600;
  }
  >li.active>a,
  >li.active>a:hover,
  >li.active>a:focus {
    border-top: 1px solid $hovercolor;
    border-right: 1px solid #999;
    border-left: 1px solid #999;
    border-bottom-color: transparent;
  }
  >li>a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 0px;
    color: #666;
    &:hover {border-color: #eee #eee #ddd;}
  }
}
.nav {
  >li>a:hover,
  >li>a:focus {
    text-decoration: none;
    background-color: #eee;
    color: #666;
  }
}
.tab-content {
  padding: 24px;
  border: 1px solid #999;
}


/* Accordion */
.panel-group {
  .panel {
    margin-bottom: 0;
    border-radius: 0px;
  }
}
.panel-default {
  border-color: #999;
  >.panel-heading {
    color: #333;
    background-color: #fff;
    border-color: #ddd;
  }
}
.panel {
  margin-bottom: 20px;
  background-color: #fff;
  /*border: 1px solid transparent;*/
  border-radius: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.panel-heading {
  padding: 9px 15px;
  &:hover {background-color: #eee;}
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 600;
  a:hover {  text-decoration: none;  }
}


/* Carousel */
.carousel-inner>.item>a>img,
.carousel-inner>.item>img,
.img-responsive,
.thumbnail a>img,
.thumbnail>img {  display: inline-block;  }


/* Forms */
.form-group {
//  display: block;
}

#success_message{ display: none;}

label.error{
  color: red !important;
}

label.valid{
  margin-left: 5px !important;
  color: green !important;}

label {
//  font-weight: 600;  width: 100%;
}
label.error {
//  text-align: left;
//  margin-bottom: 19px;
//  background: none;
//  color: red;
//  font-size: 12px;
//  font-weight: 400;
//  font-style: oblique;
}
input[type="text"], textarea {
  display: block;
  margin-bottom: 16px;
  padding: 7px 8px;
  max-width: 100%;
  width: 240px;
  border: 1px solid $bg-medium;
  border-radius: 3px;
  color: #959595;
  font: 12px/1.6 $font-regular, Helvetica, Arial, sans-serif;
  font-weight: 400;
  font-weight: 500;
  -webkit-transition: border linear .3s, box-shadow linear .3s;
  -moz-transition: border linear .3s, box-shadow linear .3s;
  -ms-transition: border linear .3s, box-shadow linear .3s;
  -o-transition: border linear .3s, box-shadow linear .3s;
  transition: border linear .3s, box-shadow linear .3s;
}
textarea {
  min-width: 97%;
  max-width: 97%;
  resize: none;
  -webkit-resize: none;
  -moz-resize: none;
  -webkit-resize: none;
  -moz-resize: none;
}
input[type="submit"], input[type="reset"], input[type="button"], button, .button {
  display: inline-block;
  margin: 0 5px 15px 0;
  padding: 7px 20px 8px;
  border-radius: 3px;
  color: #fff;
  font-weight: 800;
  font-size: 12px;
  font-family: $font-regular, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  cursor: pointer;
  -webkit-transition: background .2s ease-out;
  -moz-transition: background .2s ease-out;
  -ms-transition: background .2s ease-out;
  -o-transition: background .2s ease-out;
  transition: background .2s ease-out;
  -webkit-font-smoothing: antialiased;
}
input[type="text"], input[type="email"], input[type="password"], input[type="search"], input[type="url"], textarea {
  padding: 6px 10px;
  border: 1px solid $bg-medium;
}

.form-control {
  border: 1px solid $bg-medium;
}

input[type="search"] {  -webkit-appearance: textfield;  }
input[type="submit"]:hover, input[type="reset"]:hover, input[type="button"]:hover, button:hover, .button:hover {
  color: #fff;
}
select:focus {}
input:focus, textarea:focus {
  outline: 0;
  border-color: $bg-dark;
  //  background-color: #fff !important;
  background-image: none;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.04), inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}
button, input, select, textarea {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  max-width: 100%;
  vertical-align: baseline;
  font-size: 100%;
}



/*------------------------------------------------------------------
[5. Buttons]
*/


/* Button Sizes */
.btn-lg {font-size: 15px;}
.btn-default {font-size: 13px;}
.btn-sm {font-size: 12px;}
.btn-xs {font-size: 11px; padding: 3px 9px;}

/* Button Types */
.btn-darker,
.btn-blk,
.btn-gr,
.btn-lgr,
.btn-lgr-str,
.btn-grey-str,
.btn-light,
.btn-wh,
.btn-wh-str,
.btn-rd {
  border-radius: 24px;
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
}
.btn-darker {
  font-family: $font-accident-two;
  font-style: oblique;
  border: 1px solid $bg-darker;
  background: $bg-darker;
  color: $fontcolor-invert !important;;
  &:hover {
    border: 1px solid $hovercolor; background: $hovercolor; color: $fontcolor-invert !important;;
  }
}
.btn-blk {
  font-family: $font-accident-two;
  font-style: oblique;
  border: 1px solid #353535;
  background: #353535;
  color: $fontcolor-invert !important;;
  &:hover {
    border: 1px solid $hovercolor; background: $hovercolor; color: $fontcolor-invert !important;;
  }
}
.btn-gr {
  font-family: $font-accident-two;
  font-style: oblique;
  border: 1px solid #505050;
  color: $fontcolor-invert !important;;
  background: #505050;
  &:hover {
    border: 1px solid $hovercolor; background: $hovercolor; color: $fontcolor-invert !important;;
  }
}
.btn-lgr {
  font-family: $font-accident-two;
  font-style: oblique;
  border: 1px solid #999999;
  background: #999999;
  color: $fontcolor-invert !important;;
  &:hover {
    border: 1px solid $hovercolor; background: $hovercolor; color: $fontcolor-invert !important;;
  }
}
.btn-lgr-str {
  font-family: $font-accident-two;
  font-style: oblique;
  font-style: oblique;
  background: none;
  border: 1px solid #333;
  color: $fontcolor-regular;
  a {color: $fontcolor-regular;}
  &:hover {
    background: none;
    border: 1px solid $hovercolor; color: $hovercolor !important;;
  }
}
.btn-grey-str {
  font-family: $font-accident-two;
  font-style: oblique;
  background:  none;
  margin-right: 12px;
  border: 2px solid $fontcolor-regular;
  color: $fontcolor-regular !important;;
  &:hover {
    background: none; border: 2px solid $hovercolor; color: $hovercolor !important;;
  }
}
.btn-light {
  font-family: $font-accident-two;
  font-style: oblique;
  background:  $bg-light;
  margin-right: 12px;
  border: 1px solid $bg-light;
  color: $bg-darker !important;
  &:hover {
    background: $hovercolor; border: 1px solid $hovercolor; color: $fontcolor-invert !important;;
  }
}
.btn-wh-str {
  font-family: $font-accident-two;
  font-style: oblique;
  background:  none;
  margin-right: 12px;
  border: 2px solid $fontcolor-medium-light;
  color: $fontcolor-medium-light !important;;
  &:hover {
    background: none; border: 2px solid $hovercolor; color: $hovercolor !important;
  }
}
.btn-wh {
  font-family: $font-accident-two;
  font-style: oblique;
  background:  $bg-skin;
  margin-right: 12px;
  border: 2px solid $bg-skin;
  color: $fontcolor-regular !important;;
  &:hover {
    background: $hovercolor; border: 2px solid $hovercolor; color: $fontcolor-invert !important;
  }
}
.btn-rd {
  font-family: $font-accident-two;
  font-style: oblique;
  border: 1px solid $hovercolor;
  color: $fontcolor-invert !important;;
  background: $hovercolor;
  &:hover {
    border: 1px solid #ec1717; background: #ec1717; color: $fontcolor-invert !important;;
  }
}





/*------------------------------------------------------------------
[7. Drop-down menu]
*/

/* Superfish Drop Down menu */


.sf-menu, .sf-menu * {
  margin: 0;
  padding: 0;
  list-style: none;
}
.sf-menu {
  li {
    position: relative;
    white-space: nowrap; /* no need for Supersubs plugin */
    *white-space: normal; /* ...unless you support IE7 (let it wrap) */
    //    -webkit-transition: background .2s;
    //    transition: background .2s;
    border: none;
    text-align: left;
    a {  border: none;  text-align: left;  }
    &:hover, &.sfHover {
      /* only transition out, not in */
      -webkit-transition: none;
      transition: none;
      > ul {  display: inline-block;  }
    }
  }

  > li {
    display: inline-block;
  }
  ul {
    position: absolute;
    display: none;
    top: 132%;
    left: 0;
    white-space: nowrap;
    z-index: 99999;
    min-width: 12em; /* allow long menu items to determine submenu width */
    *width: 12em; /* no auto sub width for IE7, see white-space comment below */
    li {
      box-shadow: 0 4px 4px rgba(0,0,0,0.2);
      ul {  top: 0;  left: 100%;
        li {}
      }
    }
  }
  a {
    display: block;
    font-size: 12px;
    position: relative;
    //    border-left: 1px solid $fontcolor-invert;
    padding: 1em 1em;
    text-decoration: none;
    zoom: 1; /* IE7 */
  }
}

.sf-menu > li:hover, .sf-menu > li.sfHover {  background: none;  }
ul.sf-menu > li, .sf-menu > li > a {  border: none;  text-align: left;  }

.submenu a {  color: $fontcolor-regular; }
.submenu li:hover > a {color: $fontcolor-invert !important;}

// mega menu dropdown
.sf-menu li:hover > .sf-mega,
.sf-menu li.sfHover > .sf-mega {
  display: block;
}
.sf-mega {
  position: absolute;
  display: none;
  top: 100%;
  left: 0;
  z-index: 99;
}
.sf-mega {
  background-color: #CFDEFF;
  padding: 1em;
  box-sizing: border-box;
  width: 100%;
}
.sf-mega-section {
  float: left;
  width: 8em; /* optional */
  padding: 0 1em 1em 0;
  margin-right: 1em;
  border-right: 1px solid #b4c8f5;
}
.sf-menu .sf-mega {
  box-shadow: 2px 3px 6px rgba(0,0,0,.2);
  width: 100%; /* allow long menu items to determine submenu width */
}
.mega-item {
  position: static;
}

// arrows (for all except IE7)
.sf-arrows {
  .sf-with-ul {
    padding-right: 2.5em;
    *padding-right: 1em; /* no CSS arrows for IE7 (lack pseudo-elements) */
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 1.5em;
      margin-top: -1px;
      height: 0;
      width: 0;
      /* order of following 3 rules important for fallbacks to work */
      border: 4px solid transparent;
      /*border-top-color: #333; *//* edit this to suit design (no rgba in IE8) */
      border-top-color: rgba(10,10,10,.5);
    }
  }
  > li > .sf-with-ul:focus:after,
  > li:hover > .sf-with-ul:after,
  > .sfHover > .sf-with-ul:after {border-top-color: #333; /* IE8 fallback colour */}

  // styling for right-facing arrows
  ul {
    .sf-with-ul:after {
      margin-top: -4px;
      margin-right: -5px;
      border-color: transparent;
      border-left-color: $fontcolor-regular; /* edit this to suit design (no rgba in IE8) */
    }
    li > .sf-with-ul:focus:after,
    li:hover > .sf-with-ul:after,
    .sfHover > .sf-with-ul:after {border-left-color: $fontcolor-invert;}
  }
}


// Search in the Drop-down menu
.sf-menu {
  ul.search-panel {  }
  .search-panel {
    li {
      right: 90%;
      height: 42px;
      padding: 0px 8px;
    }
    input {
      outline:0;
      width: 455px !important;
      margin-top: 5px;
      height: 30px;
      border: 1px solid $fontcolor-medium-light;
      border-radius: 0;
      background: $bg-darker;
      color: $fontcolor-medium-light;
      padding: 0 10px;
      line-height: normal;
      vertical-align: middle;
    }
    .btn {
      padding: 3px 8px;
      margin: -14px 0 0 3px;
      height: 28px;
      border-radius: 0px;
      border: none;
      background: $bg-light;
      color: $fontcolor-regular;
      &:hover {background: $hovercolor; color: $fontcolor-invert}
    }
  }
}

// Superfish menu to Mobile Devices
#mobnav-btn {
  display: none;
  font-size: 21px;
  font-weight: bold;
  padding: 10px;
  cursor: pointer;
  margin-top: 18px;
  transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  -webkit-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  -ms-transition: all 0.25s ease-in-out;
}
.mobnav-subarrow {  display: none;  }



/*------------------------------------------------------------------
[6. Header Styles]
*/

/* Small Top with Contacts and Shop Features */
#sub-top-dark, #sub-top-light {
  height: 40px;
  font-size: 12px;
  i {margin-right: 4px; font-size: 14px;}
  .top-contacts, .top-shop {  padding-top: 10px;  }
  .top-contacts {
    float: left;
    div {  display: inline;  margin-right: 12px;  }
  }
  .top-shop {  float: right;  }
}



/* Site Title for Main Page */


#header-wrapper-mp {
  #site-title {
    float: left;
    h1 {
      font-size: 28px;
      margin-top: 46px;
      text-transform: uppercase;
      a {  text-decoration: none;  }
    }
  }


  /* Main menu for Main Page */
  #main-menu {
    float: right;
    border: none;
    margin-top: 39px;
    ul {
      list-style: none;
      margin-bottom: 12px;
    }
    > li {
      display: inline-block;
      margin-right: 24px;
      border: none;
    }
    li:last-child {  margin-right: 0;  }
    a {
      font-weight: 400;
      font-size: 13px;
    }
    > ul > li > a {
      text-transform: uppercase;
      font-weight: 700;
      font-size: 13px;
    }
  }
}




/* Inner Pages Site Title and Page Title*/

#header-wrapper {
  #site-title h1 {
    font-size: 38px;
    margin: 40px 0 0 0;
    text-transform: uppercase;
    color: $bg-dark;
    a {
      color: $bg-dark;
      text-decoration: none;
//          transition: all 0.3s ease-in-out;
//          -moz-transition: all 0.3s ease-in-out;
//          -webkit-transition: all 0.3s ease-in-out;
//          -o-transition: all 0.3s ease-in-out;
//          -ms-transition: all 0.3s ease-in-out;
    }
  }
  #page-title {
    height: 500px;
    width: 100%;
    padding: 80px 0 70px 0;
    .page-title-details {
      top: 25%;
      position: relative;
      h1 {
        font-size: 60px;
        text-transform: uppercase;
        line-height: 66px;
//        color: $fontcolor-invert !important;
      }
      ul {
        padding: 2px 12px 2px 12px;
        display: inline-block;
        margin: 0 auto;
        min-width: 30%;
        background: rgba(255, 255, 255, 0.4);
        li {
          display: inline-block;
          margin-right: 8px;
          border: none;
          font-family: 'Georgia', serif;
          font-style: italic;
          color: $fontcolor-regular;
          &:last-child {margin-right: 0;}
          > a {
            font-family: 'Georgia', serif;
            font-style: italic;
            text-decoration: underline;
            color: $fontcolor-regular;
            &:hover {color: $hovercolor;}
          }
        }
      }
    }
  }
}




/* Main menu for Inner Pages */

#header-wrapper {
  #main-menu {
    ul {
      list-style: none;
      margin-bottom: 12px;
    }
    > li {
      display: inline-block;
      margin-right: 24px;
      border: none;
      &:last-child {  margin-right: 0;  }
    }
    a {
      color: #333;
      font-weight: 400;
      font-size: 13px;
    }
    > ul > li > a {
      text-transform: uppercase;
      color: #333;
      font-weight: 700;
      font-size: 13px;
    }
    a > i {  color: #333;  }
    a:hover {  color: $hovercolor;  }
    a:hover > i {  color: $hovercolor;  }
  }
}






/* Sticky Header */

// Menu over the Slider

.sticky-overlay .sticky-wrapper {
  position: relative;
  z-index: 999;
}
.head-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#header-wrapper {
  .sticky-header {
    height: 150px;
  }
}

#header-wrapper-mp {
  .sticky-header {
    height: 120px;
  }
  #site-title h1, #main-menu {
    transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    transition-delay: 0.5s;
  }
}

#header-wrapper,
#header-wrapper-mp {
  width: 100%;
  text-align: center;
  box-sizing:border-box;
}

.sticky-header {
  position: relative;
  top: 40;
  right: 0;
  left: 0;
  z-index: 9999;
  box-sizing:border-box;
}

#header-wrapper, #header-wrapper-mp {
  .is-sticky {
    .sticky-header {
      filter: alpha(opacity=90);
      height: 80px;
      top: 0;
      -webkit-transition:width 0.5s, height 0.5s, background-color 0.1s,
      -webkit-transform 0.5s;
      transition:width 0.5s, height 0.5s, background-color 0.1s,
      transform 0.5s;
      transition-delay: 0.1s;
    }
    > .sticky-header > .mp-nav {  border-bottom: none;  }
    #site-title {  float: left;  }
    #site-title h1 {
      font-size: 28px;
      margin-top: 26px;
    }
    #site-title h1 a {
      text-decoration: none;
    }
    #main-menu {
      float: right;
      border: none;
      margin-top: 18px;
      > ul > li > a {
        text-transform: uppercase;
        font-weight: 700;
        font-size: 13px;
      }
    }
  }
}





/* Header and Initial menu Colors -------------------------------------------------
*/

// ------- Sub top
#sub-top-dark {
  background-color: $bg-darker;
  color: $fontcolor-medium-light;
  a, i {color: $fontcolor-medium-light;}
}
#sub-top-light {
  background-color: #f2f2f2;
  color: $fontcolor-regular;
  a, i {color: $fontcolor-regular;}
}


// ------- Main Page
#header-wrapper-mp {

//  Dark Header
  .header-dark {
    background: $bg-dark;
    #site-title  { h1 {  color: $fontcolor-invert; a {  color: $fontcolor-invert;  } } }
    #mobnav-btn i {color: $fontcolor-invert;}
    #main-menu { a { > i {  color: $bg-light;  } &:hover {  color: $fontcolor-invert;  } }
      > ul > li > a {  color: $bg-light;  }
      > ul > li > .sf-with-ul:after{  border-top-color: $fontcolor-invert;  }
    }
    .overlay-border {
      border-top: 1px dotted rgba(255, 255, 255, .6);
      border-bottom: 1px dotted rgba(255, 255, 255, .6);
//      hr {border: none; width: 60px;}
      margin-top: 24px;
      padding: 4px 0 6px 0;
      text-align: left;
      color: $fontcolor-invert;
      a {color: $fontcolor-invert; &:hover {color: $fontcolor-invert; text-decoration: underline;}}
      span {font-size: 20px; margin-right: 8px; display: inline-block;}
      input {
        color: $fontcolor-invert;
        border: 1px dotted rgba(255, 255, 255, .6);
        border-radius: 0;
        width: 100%;
        height: 32px;
        background: none;
      }
      h4 {text-transform: uppercase; font-size: 14px; font-family: $font-accident-one-bold; opacity: .6; display: inline-block;}
      p {font-size: 12px; opacity: .6;}
    }
  }

//  Light Header
  .header-light {
    background: #fff;
    #site-title  { h1 {  color: $bg-dark; a {  color: $bg-dark;  } } }
    #mobnav-btn i {color: $bg-dark;}
    #main-menu { a { > i {  color: $fontcolor-regular;  } &:hover {  color: $hovercolor;  } }
      > ul > li > a {  color: $bg-dark;  }
      > ul > li > .sf-with-ul:after{  border-top-color: $bg-dark;  }
    }
    .overlay-border {
      border-top: 1px dotted rgba(0, 0, 0, .6);
      border-bottom: 1px dotted rgba(0, 0, 0, .6);
//      hr {border-bottom: 1px solid rgba(0, 0, 0, .6); width: 60px;}
      margin-top: 24px;
      padding: 4px 0 6px 0;
      text-align: left;
      color: $fontcolor-regular;
      a {color: $fontcolor-regular; &:hover {color: $fontcolor-regular; text-decoration: underline;}}
      span {font-size: 20px; margin-right: 8px; display: inline-block;}
      input {
        color: $fontcolor-regular;
        border: 1px dotted rgba(0, 0, 0, .6);
        border-radius: 0;
        width: 100%;
        height: 32px;
        background: none;
      }
      h4 {text-transform: uppercase; font-size: 14px; font-family: $font-accident-one-bold; opacity: .6; display: inline-block;}
      p {font-size: 12px; opacity: .6;}
    }
  }
}


// ------- Inner Page
#header-wrapper {

  //  Header Dark
  .header-dark {
    #mobnav-btn i {color: $fontcolor-invert;}
    #site-title {
      h1 {
        color: $fontcolor-regular;
        a {  color: $fontcolor-regular;
        }
      }
    }
    .sf-menu {
      ul {
        li {
          background: $bg-dark;
          opacity: 1;
          ul {
            li {
              background: $bg-dark; opacity: 1;
            }
          }
          &:hover, &.sfHover {
            background: $bg-dark;
          }
        }
      }
    }
  }

  //  Header Light
  .header-light {
    background: $fontcolor-invert;
    #mobnav-btn i {color: $bg-dark;}
    .sf-menu {
      ul {
        li {
          background: $bg-dark;
          opacity: 1;
          ul {
            li {
              background: $bg-dark; opacity: 1;
            }
          }
          &:hover, &.sfHover {
            background: $bg-dark;
          }
        }
      }
    }
  }
}

// Disabling the Background on Overlay

#header-wrapper-mp .nobg{
  background: none;
}


/*------------------------------------------------------------------
[8. Sticky Header Menu Colors]
*/

// Dark

#header-wrapper.sticky-menu-dark,
#header-wrapper-mp.sticky-menu-dark {
  .is-sticky {
    #main-menu {
      .sf-arrows > li > .sf-with-ul:focus:after,
      .sf-arrows > li:hover > .sf-with-ul:after,
      .sf-arrows > .sfHover > .sf-with-ul:after {  border-top-color: $fontcolor-invert;  }
    }
    #main-menu {
      a > i {  color: $fontcolor-invert !important;  }
      a { > i {  color: $bg-light !important;  } &:hover {  color: $fontcolor-invert;  } }
      > ul > li > a {  color: $bg-light;  }
      > ul > li > .sf-with-ul:after{  border-top-color: $fontcolor-invert !important;  }
    }
    .overlay-border {  display: none;  }

    #mobnav-btn i {color: $fontcolor-invert !important;}

    #site-title  {
      h1 {  color: $fontcolor-invert !important; a {  color: $fontcolor-invert !important;  } } }
    .sticky-header {  background: $bg-dark;  opacity: 1;  }
  }
  //  Drop-down menu
  .sf-menu {
    ul {
      li {
        background: $fontcolor-invert;
        opacity: 1;
        ul {
          li {
            background: $fontcolor-invert; opacity: 1;
            &:hover, &.sfHover {  background: $bg-dark;  }
          }
        }
        &:hover, &.sfHover {  background: $bg-dark;  }
        &:hover, &.sfHover > a {color: $fontcolor-invert !important;}
      }
    }
  }
}


// Light

#header-wrapper.sticky-menu-light,
#header-wrapper-mp.sticky-menu-light {
  .is-sticky {
    #main-menu {
//      a > i {  color: $bg-dark;  }
//      > ul > li > .sf-with-ul:after {border-top-color: $fontcolor-regular;}
//      > ul > li > a {  color: $bg-light; &:hover{  color: $fontcolor-invert;  } }
    }
//    #main-menu > ul > li > a,
    #main-menu {
      a > i {  color: $bg-dark;  }
      > ul > li > .sf-with-ul:after {border-top-color: $fontcolor-regular;}
      > ul > li > a {  color: $bg-light; &:hover{  color: $fontcolor-invert;  } }
      a { > i {  color: $fontcolor-regular;  } &:hover {  color: $hovercolor;  } }
      > ul > li > a {  color: $bg-dark; &:hover{  color: $hovercolor;  } }
      > ul > li > .sf-with-ul:after{  border-top-color: $bg-dark;  }
    }
    .overlay-border {  display: none;  }

    #mobnav-btn i {color: $fontcolor-regular !important;}

    #site-title  {
      h1 {  color: $bg-dark !important; a {  color: $bg-dark !important;  } } }
    .sticky-header {  background: #fff;  opacity: 1;   box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3); }
  }
  //  Drop-down menu
  .sf-menu {
    ul {
      li {
        background: $fontcolor-invert;
        opacity: 1;
        ul {
          li {
            background: $fontcolor-invert; opacity: 1;
            &:hover, &.sfHover {  background: $bg-dark;  }
          }
        }
        &:hover, &.sfHover {  background: $bg-dark;  }
        &:hover, &.sfHover > a {color: $fontcolor-invert !important;}
      }
    }
  }
}





/* Slider Revolution */

.boxedcontainer {
  max-width: 1170px;
  margin: auto;
  padding: 0px 30px;
}
.tp-banner-container{
  width:100%;
  position:relative;
  padding:0;
  /*margin-bottom: 64px;*/
//  margin-top: -32px;
}
.tp-banner{
  width:100%;
  position:relative;
//  text-align: center;
//  height: 800px !important;
}
.tp-banner-fullscreen-container {
  width:100%;
  position:relative;
  padding:0;
}
.tp-caption {
//  text-align: center;
  text-align: left;
  a {
    color: $fontcolor-invert;
    &:hover {
      color: $hovercolor;
    }
  }
}

// Captions Styles
.tp-caption a {  margin: 0;  }
.cap-title {
  font-size: 80px;
  text-transform: uppercase;
}
.cap-subtitle {
  font-size: 15px;
  text-transform: uppercase;
}
.cap-title {color: #fff;}
.cap-subtitle {color: #fff;}


/* Homepage block */
.infoblock {
  @include infoblock-margins;
  hr {
    margin-top: 18px;
    border-top: 3px solid $bg-dark;
  }
  a {
	color: $fontcolor-regular;
	&:hover {
	  text-decoration: none;
	  color: $hovercolor;
	}
  }
}

.hp-block {
  a {  color: $fontcolor-regular;  }
  .hp-categories-menu {
    text-align: right;
    ul {
      list-style: none;
      text-align: right;
      display: inline-block;
      white-space: pre-line;
      width: auto;
      padding-left: 0;
      li {
        position: relative;
        background: $bg-dark;
        display: inline-block;
        padding: 8px 20px;
        margin-bottom: 1px;
        a {
          color: $fontcolor-invert;
          text-decoration: none;
          cursor: pointer;
          font-size: 14px;
        }
        &:hover {  background: $hovercolor;  }
        &:hover:after, &:focus:after {
          content: '';
          position: absolute;
          border-style: solid;
          border-width: 20px 0 20px 8px;
          border-color: transparent $hovercolor;
          display: block;
          width: 0;
          z-index: 1;
          margin-top: -21px;
          right: -8px;
          top: 50%;
        }
      }
      li.active {  background: $hovercolor;  }
    }
  }
  .selected img {  opacity:0.5;  }
  .list-inline {
    margin: 0 auto;
    list-style: none;
  }
  .list-inline>li {
    display: inline-block;
    padding-right: 4px;
    padding-left: 4px;
  }
  .carousel-controls {
    width: 90%;
    margin: 0 auto;
    .carousel-control-left {
      display: inline;
      float: left;
      width: 32px;
      height: 8px;
      background: url("../images/arrow-left.png");
      &:hover {
        background: url("../images/arrow-left-hover.png");
        -webkit-transition: all .5s ease;
        -o-transition: all .5s ease;
        transition: all .5s ease;
      }
    }
    .carousel-control-right {
      display: inline;
      float: right;
      width: 32px;
      height: 8px;
      background: url("../images/arrow-right.png");
      transition: all 0.35s ease-in-out;
      -moz-transition: all 0.35s ease-in-out;
      -webkit-transition: all 0.35s ease-in-out;
      -o-transition: all 0.35s ease-in-out;
      -ms-transition: all 0.35s ease-in-out;
      &:hover {  background: url("../images/arrow-right-hover.png");  }
    }
  }
}


/* Articles at Homepage */
.author-userpic {  display: inline-block;  }

/* FlexSlider */
.second-slider {
  text-align: center;
  .second-slider-annotation {
    text-align: center;
    margin: 0 auto;
  }
  .flex-direction-nav .flex-prev {padding: 0 0 0 0; }
  .flex-direction-nav .flex-next {padding: 0 18px 0 0;}
}
.flexslider a {  color: #fff;  }


/* Portfolio Block */
.portfolio-hp {
  h2 {  color: #333;  }
  .mag-content {
    margin: 0 auto;
    max-width: 1170px;
  }
}


/* Isotope */

.isotope-container {
  .row {
    margin-left:0;
    margin-right:0;
    .item {  border: none;  }
  }
}


/* Isotope Transitions
------------------------------- */
.isotope,
.isotope .item {
  -webkit-transition-duration: 0.8s;
  -moz-transition-duration: 0.8s;
  -ms-transition-duration: 0.8s;
  -o-transition-duration: 0.8s;
  transition-duration: 0.8s;
}
.isotope {
  -webkit-transition-property: height, width;
  -moz-transition-property: height, width;
  -ms-transition-property: height, width;
  -o-transition-property: height, width;
  transition-property: height, width;
}
.isotope .item {
  -webkit-transition-property: -webkit-transform, opacity;
  -moz-transition-property:    -moz-transform, opacity;
  -ms-transition-property:     -ms-transform, opacity;
  -o-transition-property:         top, left, opacity;
  transition-property:         transform, opacity;
}

/* responsive media queries */
@media (max-width: 768px) {

}


/* Counts Mainpage Block */

.bg-light .counts {  color: $bg-dark;  }
.bg-dark .counts {color: $fontcolor-invert;}

.counts {
  padding: 50px 0 60px 0;
  text-align: center;
  .count-container {
    text-align: center;
    .count {
      text-align: center;
      display: inline-block;
      opacity: .8;
      transition: all 0.35s ease-in-out;
      -moz-transition: all 0.35s ease-in-out;
      -webkit-transition: all 0.35s ease-in-out;
      -o-transition: all 0.35s ease-in-out;
      -ms-transition: all 0.35s ease-in-out;
      transition-delay: .1s;
      &:hover { color: $bg-accent; opacity: 1;  cursor: pointer;  }
      .count-icon {
        margin: 0;
        padding: 0;
        font-size: 25px;
      }
      .count-text {
        margin-top: 8px;
        font-size: 12px;
        text-transform: uppercase;
      }
    }
  }
}
.digit {
  font-size: 72px;
  line-height: 46px;
  margin: 0;
  padding: 0;
}


/* Details on Mainpage */

.hp-details {
  span {
    font-size: 32px; color: $bg-accent;
    line-height: 40px;
  }
}


/* Features Slider */

.hp-features-slider {  }

/* Flickity */

.flickity-enabled {
  position: relative;
  &:focus { outline: none; }
}
.flickity-viewport {
  overflow: hidden;
  position: relative;
  cursor: -webkit-grab;
  cursor: grab;
}
.flickity-viewport.is-pointer-down {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}
.flickity-slider {
  position: absolute;
  width: 100%;
}

/* ---- previous/next buttons ---- */
.flickity-prev-next-button {
  position: absolute;
  top: 50%;
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: white;
  background: hsla(0, 0%, 100%, 0.75);
  cursor: pointer;
  /* vertically center */
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.flickity-prev-next-button.previous { left: 10px; }
.flickity-prev-next-button.next { right: 10px; }
.flickity-prev-next-button:disabled {  opacity: 0.3;  cursor: auto;  }
.flickity-prev-next-button svg {
  position: absolute;
  left: 20%;
  top: 20%;
  width: 60%;
  height: 60%;
}
.flickity-prev-next-button .arrow {  fill: #333;  }

/* color & size if no SVG - IE8 and Android 2.3 */
.flickity-prev-next-button.no-svg {  color: #333;  font-size: 26px;  }

/* ---- page dots ---- */
.flickity-page-dots {
  position: absolute;
  width: 100%;
  bottom: -25px;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  line-height: 1;
}
.flickity-page-dots .dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 8px;
  background: #333;
  border-radius: 50%;
  opacity: 0.25;
  cursor: pointer;
}
.flickity-page-dots .dot.is-selected { opacity: 1; }
.flickity-page-dots .dot {
  width: 12px;
  height: 12px;
  opacity: 1;
//  background: transparent;
  background: $fontcolor-invert;
  opacity: .3;
//  border: 1px solid white;
  -webkit-transition: background 0.3s;
  transition: background 0.3s;
}
.flickity-page-dots .dot.is-selected {  background: $fontcolor-invert;  }


// Features

.features-bg {
  position: relative;
  min-height: 360px;
  background: url('../images/features-intro-01.jpg') no-repeat center center;
  background-size: cover;
}
.features-img {
  width: 100%;
  height: 360px;
  text-align: center;
  line-height: 400px;
}
.features-slider {
  position: relative;
  padding: 50px 100px 30px 100px;
  height: 360px;
  background-color: $bg-accent;
  h3 {color: $fontcolor-invert;}
}
.features-slider ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.features-slider ul li {
  width: 100%;
  padding: 4px 1px;
}
.features-slider li h1 {
  margin-bottom: 15px;
  color: #fff;
  font-weight: 400;
  font-size: 22px;
}
//.features-slider li p {  color: #fff;  font-size: 14px;  }
.features-intro-img {  position: relative  }
.slides li h1 {
  margin: 0;
  padding: 0;
}
.features-slider .flickity-page-dots {
  text-align: left;
  margin-top: 50px;
  position: static;
}
.features-slider .flickity-page-dots .dot {  margin: 0 12px 0 0;  }


/* Customer's Review Mainpage Block */

.customer-review {
  .review-container {
    width: 80%;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
    .review-author {
      display: inline-block;
      text-align: center;
      margin-bottom: 40px;
      span {  color: #666;  }
    }
    .review-content {
      display: inline-block;
      text-align: left;
    }
  }
}


/* Services block */
.hp-services {
  ul {
//    list-style-image: url(../images/list-cross.png);
    margin-top: 8px;
//    border-left: 1px dashed $bg-medium;
    padding: 0 8px;
    font-size: 12px;
    font-weight: 600;
    li {
      color: $bg-dark;
      padding: 8px 0;
      border-bottom: 1px dotted $bg-medium;
      &:last-child {  border-bottom: none;  }
    }
  }
}


.rd-medal, .rd-medal-skin, .rd-medal-light, .rd-medal-dark, .rd-medal-medium, .rd-medal-outline {
  text-align: center;
//  margin: 0 auto;
  height: 100px;
  width: 100px;
  border-radius: 50px;
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
  span {
    font-size: 32px;
    line-height: 95px;
    transition: all 0.35s ease-in-out;
    -moz-transition: all 0.35s ease-in-out;
    -webkit-transition: all 0.35s ease-in-out;
    -o-transition: all 0.35s ease-in-out;
    -ms-transition: all 0.35s ease-in-out;
  }
}

.rd-medal {
  background: $bg-light;
  span {  color: $bg-dark;  }
  &:hover {
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}
.rd-medal-skin {
  background: $bg-skin;
  span {  color: $bg-accent;  }
  &:hover {
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}
.rd-medal-light {
  background: $bg-light;
  span {  color: $bg-dark;  }
  &:hover {
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}
.rd-medal-dark {
  background: $bg-dark;
  span {  color: $fontcolor-invert;  }
  &:hover {
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}
.rd-medal-medium {
  background: $bg-medium;
  span {  color: $fontcolor-invert;  }
  &:hover {
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}
.rd-medal-outline {
  border: 2px solid $bg-accent;
  span {  color: $bg-accent;  }
  &:hover {
    border-color: $hovercolor;
    background: $hovercolor;
    span {  color: $fontcolor-invert;  }
  }
}


/* Portfolio Mainpage Block */

.logos {
  .logos-filter {
    ul {
      list-style: none;
      padding: 0;
      li {
        display: inline-block;
        margin-right: 32px;
        &:last-child {margin-right: none;}
        a {
          font-family: $font-accident-two;
          font-size: 18px;
          font-style: oblique;
          color: $fontcolor-medium-light;
          &:hover {
            text-decoration: underline;
            color: $fontcolor-invert;
          }
          &:active {color: $fontcolor-invert;}
        }
      }
    }
  }
  .logos-item {
    text-align: center;
    h3 {
      font-family: $font-accident-two;
      font-style: oblique;
//      a {  color: $fontcolor-invert;  }
    }
//    p {  color: $fontcolor-medium-light;  }
    @mixin portf-item-hp {
      width: 120px;
      height: 133px;
      margin: 0 auto 30px auto;
    }
    .logos-pic-01 {
      @include portf-item-hp;
      background: url("../images/hp-portf-01.png") center no-repeat;
    }
    .logos-pic-02 {
      @include portf-item-hp;
      background: url("../images/hp-portf-02.png") center no-repeat;
    }
    .logos-pic-03 {
      @include portf-item-hp;
      background: url("../images/hp-portf-03.png") center no-repeat;
    }
    .logos-pic-04 {
      @include portf-item-hp;
      background: url("../images/hp-portf-04.png") center no-repeat;
    }
  }
  .btn-logos {  min-width: 180px;  }
}


/* Blog box on MainPage */

.blog-hp {
  a {
    color: $fontcolor-regular;
    span {  color: #999;  }
  }
  .article-author-hp {
    .article-author-name {
      padding-top: 14px;
      h4 {  font-family: $font-regular;  }
      p {
        color: #999;
        line-height: 18px;
        font-style: oblique;
      }
      span a {  color: #999;
        &:hover { color: $hovercolor;}
      }
    }
  }
}



/*------------------------------------------------------------------
[9. Inner Pages]
*/



/* Contacts */

.contact-address {
  a {
    color: $fontcolor-regular;
    text-decoration: none;
    &:hover {  color: $hovercolor;  }
  }
  span {
    color: $fontcolor-regular;
    opacity: .5;
  }
}
.contacts-form {
  input, textarea {
    width: 100%;
    border-radius: 0;
    font: 12px/1.6 $font-regular, Helvetica, Arial, sans-serif;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 15px;
  }
}


/* Portfolio */

.portfolio-details {  padding: 14px;  }
#portfolio {  }
.portfolio-caption {
  width: 100%;
  background: #f2f2f2;
  padding: 12px 18px 21px 18px;
  text-align: left;
  font-size: 12px;
  p {  margin: 0; line-height: 18px; }
  .portfolio-date {
    font-size: 11px;
    line-height: 20px;
    color: #999;
    a {  color: #999; &:hover {color: $hovercolor; text-decoration: none;} }
    span { position: relative; font-size: 14px;  margin-right: 2px;  }
    p { display: inline-block; margin-right: 10px; }
  }
  a {  color: $fontcolor-regular;  }
  h4 {
    margin: 10px 0 5px 0;
    a {  color: $bg-dark; &:hover, &:active, &:focus  {color: $hovercolor; text-decoration: none;} &:visited { text-decoration: none; } }
  }
}
.portfolio-item-data {
  ul {  padding-left: 0;
    li { span {  font-weight: 600;  } }
  }
}

.port-filter {
  ul {  list-style: none;  padding: 0;
    li {  display: inline-block;
      a {
        position: relative;
        padding: 6px 18px;
        margin-right: 1px;
        font-family: "Georgia", serif;
        font-size: 14px;
        font-style: oblique;
        color: $fontcolor-regular;
        transition: all 0.35s ease-in-out;
        -moz-transition: all 0.35s ease-in-out;
        -webkit-transition: all 0.35s ease-in-out;
        -o-transition: all 0.35s ease-in-out;
        -ms-transition: all 0.35s ease-in-out;
        &:after {
          content: '';
          position: absolute;
          border-style: solid;
          border-width: 8px 8px 0;
          border-color: #fff transparent;
          display: block;
          width: 0;
          z-index: 1;
          margin-left: -8px;
          bottom: -8px;
          left: 50%;
        }
        &:hover, &:focus{  text-decoration: none;  background: #f2f2f2;  }
        &:hover:after, &:focus:after {
          border-color: #f2f2f2 transparent;
          transition: all 0.35s ease-in-out;
          -moz-transition: all 0.35s ease-in-out;
          -webkit-transition: all 0.35s ease-in-out;
          -o-transition: all 0.35s ease-in-out;
          -ms-transition: all 0.35s ease-in-out;
        }
      }
    }
  }
}











/* Blog
--------------------------------------------------*/

.post {
  a {
    color: $fontcolor-regular;
    &:hover {
      color: $hovercolor;
    }
  }
  .post-title a {
    color: $fontcolor-regular;
    &:hover {
      color: $hovercolor;
      text-decoration: none;
    }
  }
}



/* Widgets
--------------------*/


// Basics

.widget {
  margin-bottom: 50px;
  width: 100%;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  -ms-hyphens: auto;
  h3 {
    text-transform: uppercase;
    margin-top: 28px;
  }
  a {
    color: $fontcolor-regular;
    span {  color: #999;  }
  }
  ul, ol {
    margin: 0;
    padding-left: 0;
    list-style: none;
  }
}
.widget_archive li:before, .widget_categories li:before, .widget_links li:before, .widget_meta li:before, .widget_nav_menu li a:before, .widget_pages li:before, .widget_recent_comments li:before, .widget_recent_entries li:before {
  display: inline-block;
  margin-right: 5px;
  margin-left: 5px;
  content: "\F105";
  vertical-align: -6%;
  font-size: 16px;
  font-family: Fontawesome;
  line-height: 1;
  color: $bg-dark;
  opacity: .3;
}
.primary-sidebar .widget li {
  border-top: 1px dotted rgba(0, 0, 0, 0.2);
  padding: 8px 0;
}
.primary-sidebar .widget li:first-child {  border-top: 0;  }
.post {  margin-bottom: 72px;  }


// Search widget

.widget_search {
  .search-submit {  display: none;  }
  .screen-reader-text {
    position: absolute;
    clip: rect(1px, 1px, 1px, 1px);
  }
}
.primary-sidebar .widget input, .primary-sidebar .widget textarea {
  width: 100%;
  &:focus {  border-color: rgba(0, 0, 0, 0.3);  }
}


// Archives widget

.widget_archives {
  .archive-month {
    width: 49%;
    padding: 6px 16px 9px 16px;
    font-size: 12px;
    //    background: #f2f2f2;
    display: inline-block;
    transition: all 0.35s ease-in-out;
    -moz-transition: all 0.35s ease-in-out;
    -webkit-transition: all 0.35s ease-in-out;
    -o-transition: all 0.35s ease-in-out;
    -ms-transition: all 0.35s ease-in-out;
    &:hover {
      background: $bg-dark;
      color: $fontcolor-invert;
    }
  }
  .month-current {  background: $bg-light;  }
}


// Authors widget

.widget_authors {
  .author-wgt-line {
    display: inline-block;
    width: 100%;
    padding: 0 0 6px 0;
    .author-name-wgt {
      display: inline;
      float: left;
    }
    .author-count-wgt {
      display: inline;
      float: right;
      color: #999;
    }
  }
}


/* Comments */

#comments {
  margin-bottom: 60px;
  .post-data {
    .post-date {  margin-top: 2px;  }
    .post-author {  font-size: 16px;  }
  }
  .media {  margin-top: 40px;  }
  p {
    a {
      color: $fontcolor-regular;
      opacity: .5;
    }
  }
}


/* Shop */

.shop-item {
  background: #fff;
  border: 1px solid #dedede;
  min-height: 300px;
  text-align: center;
  overflow: hidden;
  position: relative;
  margin-bottom: 60px;
  .item-photo {
    background: #fff;
    min-height: 200px;
    margin: 0 auto;
    padding: 24px 0 32px 0;
    img {  margin: 0 auto;  }
  }
  .item-attrs {
    padding: 9px 16px 0 16px;
    .item-oldprice {
      font-size: 18px;
      line-height: 18px;
      color: #999;
      text-decoration: line-through;
    }
    .item-newprice {
      font-size: 30px;
      line-height: 30px;
      color: #333;
    }
    .add-to-cart {
      i {
        color: #999;
        font-size: 18px;
        margin-top: 16px;
      }
      a {
        text-transform: uppercase;
        font-size: 14px;
        color: $fontcolor-regular;
        &:hover, &:hover i {  color: $hovercolor;  }
      }
      border-top: 1px solid #999;
      margin-top: 24px;
    }
  }
  .item-content {
    background: #f5f5f5;
    font-size: 11px;
    padding: 16px;
    p {  color: $fontcolor-regular;  }
  }
  &:hover {  border: 1px solid $hovercolor;  }
  &:hover .add-to-cart {  border-top: 1px solid $hovercolor;  }
  &:hover .item-newprice,
  &:hover .add-to-cart i,
  &:hover .add-to-cart a {  color: $hovercolor;  }
}

.block-dark .shop-item {
  border: none;
  &:hover {  border: none;  }
}

.shop-item,
.shop-item .item-attrs .item-newprice,
.shop-item .item-attrs .add-to-cart,
.shop-item .item-attrs .add-to-cart i,
.shop-item .item-attrs .add-to-cart a {
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
}
.onsale {
  top: -50px;
  left: -50px;
  right: auto;
  position: absolute;
  z-index: auto;
  padding-top: 80px;
  width: 100px;
  height: 100px;
  background: $hovercolor;
  color: #fff;
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 11px;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.shop-navigation {
  border-top: 1px dotted $bg-dark;
  ul {
    margin-top: 12px;
    list-style: none;
    padding-left: 0;
    display: inline-block;
    li {
      display: inline-block;
      margin-right: 24px;
      &:last-child {  margin-right: 0;  }
      a {
        font-family: $font-accident-one-normal;
        font-size: 15px;
        text-transform: uppercase;
        color: $bg-dark;
        &:hover {
          color: $hovercolor;
          text-decoration: none;
        }
      }
    }
  }
}
.shop-item-description {
  h2 {
    font-weight: 600;
    span {
      font-weight: 400;
      font-size: 36px;
    }
  }
}
.shop-item-block {
  border: 3px solid $bg-dark;
  position: relative;
  .shop-item-price {
    display: inline-block;
    padding: 18px;
    width: 60%;
    table {
      width: 100%;
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
    .list-price {
      font-size: 18px;
      line-height: 18px;
      color: #999;
      text-decoration: line-through;
    }
    .deal-price {
      font-size: 30px;
      line-height: 30px;
      color: #333;
    }
  }
  .shop-item-attrs {
    position: absolute;
    border: 3px solid $bg-dark;
    top: -3px;
    right: -3px;
    float: right;
    width: 30%;
    .item-material,
    .item-quantity,
    .item-add {
      display: inline-block;
      padding: 10px 14px;
      width: 100%;
    }
    .item-material {  }
    .item-quantity {
      border-top: 3px solid $bg-dark;
    }
    .item-add {
      background: $bg-light;
      border-top: 3px solid $bg-dark;
      a {
        text-transform: uppercase;
        font-size: 14px;
        color: #333;
        cursor: pointer;
        i {
          font-size: 16px;
          color: $bg-dark;
        }
        &:hover, &:hover a, &:hover i {  color: $hovercolor;  }
      }
    }
  }
}


/* Content */

#content {  min-height: 600px; background: $bg-skin;  }
#content-initial {
  min-height: 1200px; background: $bg-light;
}
.down-arrow {
  background: url('../images/down_arrow.png') no-repeat center;
  min-height: 72px;
}
.down-arrow-white {
  background: url('../images/down_arrow_wh.png') no-repeat center;
  min-height: 72px;
}
p.sub-heading {
  font-family: $font-accident-two;
  font-style: oblique;
  color: #666;
  margin-top: 0;
}
.blogpost-heading { margin-bottom: 0; }
blockquote {
  border: 1px solid $bg-dark;
  padding: 30px;
  font-family: $font-accident-two;
  font-style: oblique;
  font-size: 16px;
  line-height: 26px;
  footer {
    font-family: $font-regular;
    font-size: 14px;
    color: #999;
    font-style: normal;
    text-transform: uppercase;
  }
  .alt {
    border: none;
    padding: 20px 0 24px 40px;
  }
}
blockquote footer:before, blockquote small:before, blockquote .small:before {  content: normal;  }
.blockquote-alt {
  font-family: $font-accident-two;
  font-size: 16px;
  font-style: oblique;
  border: none;
  margin-top: 20px;
  padding: 0 0 24px 40px;
  background: url("../images/blocks.png") no-repeat left top;
}


/* Post */

.post-attrib {   /* Post attributes in line */
  display: inline-block;
  vertical-align: middle;
  min-height: 60px;
}
.post-data, .post-data0, .post-data1, .post-data2 { display: inline-block; }
.post-data1 {
  margin: 0 12px 24px 0;
  vertical-align: top;
}
.post-data2 {
  vertical-align: middle;
  margin-bottom: 22px;
}
.post-date {
  font-family: 'Oxygen', sans-serif;
  font-style: oblique;
  font-size: 12px;
  line-height: 14px;
  margin-top: 8px;
  color: #666;
}
.post-title {
  font-family: $font-accident-two;
  font-style: oblique;
  /*font-size: 28px;*/
  line-height: 28px;
  margin-top: 4px;
}
.post-author {
  font-family: $font-accident-two;
  font-size: 14px;
  font-style: oblique;
  margin-bottom: 5px;
  a {  color: #333;  }
}
.comment-baloon {
  display: inline-block;
  background: $bg-dark;
  height: 44px;
  width: 44px;
  border-radius: 22px;
  color: $fontcolor-invert;
  text-align: center;
  vertical-align: middle;
  line-height: 44px;
  margin: 6px 8px 24px 0;
}
.post-tags {
  margin-bottom: 18px;
  font-size: 12px;

  a {
    display: inline-block;
    background: $bg-light;
    border: 1px solid $bg-light;
    padding: 2px 12px;
    margin: 0 2px 4px 0;
    color: $fontcolor-regular;
    opacity: .6;
    -webkit-transition: all .5s ease;
    -o-transition: all .5s ease;
    transition: all .5s ease;
    &:hover {
      background: $fontcolor-invert;
      border: 1px solid $bg-dark;
	  color: $fontcolor-regular;
      opacity: 1;
      text-decoration: none;
    }
  }
}

// Chat
.chat-companion {
  color: $hovercolor;
  text-transform: uppercase;
  text-align: right;
  font-weight: bold;
}
.chat-me {
  color: #999;
  text-transform: uppercase;
  text-align: right;
  font-weight: bold;
}

// Link
.link-bg {
  background: $bg-light;
  padding: 50px;
  text-align: center;
  vertical-align: middle;
  font-family: $font-accident-two;
  font-style: oblique;
  font-size: 24px;
  a {
    color: #333;
    &:hover {  color: $hovercolor;  }
  }
}


/* Pricing Tables */

.price-number{  color: $fontcolor-invert;  }
.pricing {
  padding: 30px 30px 56px 30px;
  min-height: 300px;
  /*    width: 262px;*/
  text-align: center;
  box-sizing: border-box;
  border: 1px solid #f2f2f2;
  font-size: 13px;
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
  ul {
    text-align: center;
    margin: 36px 0 36px 0;
    padding: 0;
    li {
      border-top: 1px dotted #969696;
      &:last-child {  border-bottom: 1px dotted #969696; }
    }
  }
  &:hover {
	background: $fontcolor-invert;
	border: 1px dotted $hovercolor;
  }
  &:hover ul li {
	border-top: 1px dotted $hovercolor;
	&:last-child {  border-bottom: 1px dotted $hovercolor;  }
  }
  &:hover .price-number,
  &:hover .price-plan,
  &:hover .price-descr,
  &:hover .price-conclusion,
  &:hover ul li {
    color: $hovercolor;
	cursor: pointer;
  }
  &:hover .btn {
	background: $hovercolor;
	border-color: $hovercolor;
  }
}
.plain {
  background: #f2f2f2;
  color: $fontcolor-regular;
}
.proposal {
  background: #fff;
  border: 1px dotted $hovercolor;
  color: $hovercolor;
  ul {
    li {
      border-top: 1px dotted $hovercolor;
      &:last-child {  border-bottom: 1px dotted $hovercolor;  }
    }
  }
  .price-number{  color: $hovercolor;  }
}
.price-attrs {
  width: 100%;
  height: auto;
}
.price-number {
  font-size: 84px;
  line-height: 90px;
  padding-bottom: 18px;
}
.price-plan {
  font-size: 24px;
}
.price-descr {
  text-transform: uppercase;
}
.price-conclusion {  font-size: 11px;  }


/* Team */

.team-member {
  background: $bg-light;
  text-align: center;
  transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  .team-photo {
    background: $fontcolor-invert;
    min-height: 200px;
    margin: 0 auto;
    padding: 24px 0 32px 0;
  }
  .team-attrs {
    background: $fontcolor-invert;
    padding: 0px 16px 9px 16px;
    .team-name {  font-size: 21px;  }
    .team-position {  font-size: 12px;  }
  }
  .team-content {
	color: #999;
    background: #fff;
    font-size: 12px;
    padding: 16px 16px 24px 16px;
  }
}


/*------------------------------------------------------------------
[10. Hover Effects]
*/

.item-wrap {  margin-bottom: 30px;  }

figure {
  position: relative;
  overflow: hidden;
  background: $bg-dark;
  text-align: center;
  cursor: pointer;
  img {
    position: relative;
    opacity: 0.8;
  }
  figcaption {
    padding: 1.0em;
    color: $fontcolor-invert;
    text-transform: uppercase;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    > a {
      z-index: 1000;
      text-indent: 200%;
      white-space: nowrap;
      font-size: 0;
      opacity: 0;
    }
    &:before,
    &:after {
      pointer-events: none;
    }
  }
  figcaption,
  figcaption > a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  h3 {
    word-spacing: -0.15em;
    font-family: $font-accident-one-normal;
    span {
      font-family: $font-accident-one-medium;
    }
  }
  h3, p {
    margin: 0;
  }
  p {
    letter-spacing: 1px;
    font-size: 68.5%;
  }
}


/* Portfolio hover */

figure.effect-goliath {
  background: $bg-dark;
  figcaption {
    text-align: left;
    font-size: 12px;
    padding: 0 5.3em 0 0 !important;

    .fig-description {

    }
    a {
      color: $fontcolor-invert;
      font-size: 11px;
      line-height: 20px;
    }
    span {
      position: relative;
      margin-right: 2px;
    }
  }
  img, h3 {
    -webkit-transition: -webkit-transform 0.5s;
    transition: transform 0.5s;
  }
  img {
    max-width: none;
    width: -webkit-calc(100% + 60px);
    width: calc(100% + 60px);
    opacity: 1;
    -webkit-transition: opacity 0.5s, -webkit-transform 0.5s;
    transition: opacity 0.5s, transform 0.5s;
    -webkit-transform: translate3d(-30px,0,0) scale(1.12);
    transform: translate3d(-30px,0,0) scale(1.12);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  h3 {
    margin: 32px 0 0px 0;
    font-family: $font-accident-one-bold;
    color: $fontcolor-invert;
    text-transform: uppercase;
    padding: 18px 18px;
    font-size: 21px;
    background-color: $hovercolor;
    //    border-bottom: 1px solid rgba(255,255,255,.5);
    opacity: 0;
    -webkit-transition: opacity 0.5s, -webkit-transform 0.5s;
    transition: opacity 0.5s, transform 0.5s;
    -webkit-transform: translate3d(0,20px,0);
    transform: translate3d(0,20px,0);
  }
  p {
    margin-top: 1px;
    //    background-color: $bg-dark;
    color: $fontcolor-invert;
    padding: 8px 18px;
    text-transform: none;
    font-family: $font-regular;
    font-size: 12px;
    opacity: 0;
    -webkit-transition: opacity 0.5s, -webkit-transform 0.35s;
    transition: opacity 0.5s, transform 0.5s;
    -webkit-transform: translate3d(0,60px,0);
    transform: translate3d(0,60px,0);
  }
  &:hover img {
    opacity: 0.1;
    -webkit-transform: translate3d(0,0,0) scale(1);
    transform: translate3d(0,0,0) scale(1);
  }
  &:hover h3 {
    opacity: 1;
    -webkit-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0);
  }
  &:hover p {
    width: 100%;
    opacity: 1;
    -webkit-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0);
  }
}
.captions {
  &:hover img {  opacity: 0.8 !important;  }
}


/* Team Hover */

figure.effect-zoe {
  margin: 0;
  width: 100%;
  height: auto;
  min-width: 200px;
  max-height: none;
  max-width: none;
  float: none;
  img {
    display: inline-block;
	opacity: 1;
  }
  p.icon-links a {
    float: right;
    color: $fontcolor-invert;
    font-size: 2.2em;
	opacity: .9;
    &:hover, &:focus {
      color: $fontcolor-invert;
	  opacity: 1;
    }
    -webkit-transition: -webkit-transform 0.35s;
    transition: transform 0.35s;
    -webkit-transform: translate3d(0,200%,0);
    transform: translate3d(0,200%,0);
    span::before {
      display: inline-block;
      padding: 18px 10px;
      font-family: 'feather';
      speak: none;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
  figcaption {
    top: auto;
    bottom: 0;
    padding: 1em;
    height: 6em;
    background: $hovercolor;
    color: $fontcolor-invert;
    -webkit-transition: -webkit-transform 0.35s;
    transition: transform 0.35s;
    -webkit-transform: translate3d(0,100%,0);
    transform: translate3d(0,100%,0);
  }
  .icon-eye::before {  content: '\e000';  }
  .icon-paper-clip::before {  content: '\e001';  }
  .icon-heart::before {  content: '\e024';  }
}
figure.effect-zoe:hover figcaption,
figure.effect-zoe:hover h2,
figure.effect-zoe:hover p.icon-links a {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
figure.effect-zoe:hover p.icon-links a:nth-child(3) {
  -webkit-transition-delay: 0.1s;
  transition-delay: 0.1s;
}
figure.effect-zoe:hover p.icon-links a:nth-child(2) {
  -webkit-transition-delay: 0.15s;
  transition-delay: 0.15s;
}
figure.effect-zoe:hover p.icon-links a:first-child {
  -webkit-transition-delay: 0.2s;
  transition-delay: 0.2s;
}



/*------------------------------------------------------------------
[11. Google Map]
*/

#google-map {
  width: 100%;
  img {  max-width: none;  }
}
.bigmap {  height: 300px !important;  }


/* Google map Slide-out */
#gm-panel {
  width: 100%;
  background: #eeeeee;
  text-align: center;
  /*z-index: 9;*/
  bottom: 300px;
  display:none;
}
a.gm-toggle-link {  cursor: pointer;  }
.gm-toggle  {
  margin: 0 auto;
  padding: 12px 0;
  text-align: center;
  background: $bg-darker;
  width: 100%;
  i {
    color: $fontcolor-invert;
    font-size: 18px;
    line-height: 18px;
  }
  transition: all 0.35s ease-in-out;
  -moz-transition: all 0.35s ease-in-out;
  -webkit-transition: all 0.35s ease-in-out;
  -o-transition: all 0.35s ease-in-out;
  -ms-transition: all 0.35s ease-in-out;
  &:hover {  background: $hovercolor;  }
}


/*------------------------------------------------------------------
[12. Footer]
*/

#footer {
  background: $bg-dark;
  min-height: 350px;
  color: $fontcolor-medium-light;
  font-size: 12px;
  padding: 48px 0 0 0;
  position: relative;
  h2 {
    font-size: 24px;
    text-transform: uppercase;
    margin-bottom: 24px;
  }
  p {  margin-bottom: 10px;  }
  a {
    color: $fontcolor-medium-light;
    font-size: 12px;
    text-decoration: underline;
    &:hover {  color: $fontcolor-invert;  }
  }
  i {  color: $fontcolor-medium-light;  }
  .contact {  display: table;  }
  .footer-addr {
    width: 100%;
    margin-bottom: 24px;
    display: table-row;
  }
  .footer-icon {
    width: 10%;
    display: table-cell;
    float: left;
    font-size: 16px;
  }
  .addr-text {
    width: 90%;
    display: table-cell;
    float: right;
    padding-top: 4px;
  }
}


/* Flickr feed */

.thumbs {
  margin: 0 0 0 -6px;
  padding: 0;
  overflow: hidden;
  li {
    list-style: none;
    float: left;
    margin: 5px;
    padding: 3px;
    /*background: #eee;*/
    /*-moz-box-shadow: 0 0 4px #444;*/
    /*-webkit-box-shadow: 0 0 2px #000;*/
    img {
      width: 40px;
      height: 40px;
      display: block;
    }
    a { img {  border: none;  } }
  }
  .img-rounded {  border-radius: 20px;  }
}
#cycle { margin: 0; padding: 0; width: 500px; height: 333px; padding: 3px; background: #eee; -moz-box-shadow: 0 0 2px #000; -webkit-box-shadow: 0 0 2px #000;}
#cycle li { position: relative; list-style: none; margin: 0; padding: 3px; width: 500px; height: 333px; overflow: hidden; }
#cycle li div { position: absolute; bottom: 3px; left: 3px; padding: 3px; width: 494px; background: black; color: white; font-size: 12px; opacity: .8; }


/* Follow us */

.follow {
  display: table;
  width: 100%;
  .follow-element {
    display: table-row;
    height: 40px;
    &:hover .follow-icon {
      border: 1px solid $bg-dark;
      border-right: none;
      background: $bg-darker;
    }
    &:hover .follow-descr {
      border: 1px solid $bg-dark;
      background: $bg-darker;
    }
    .follow-icon,
    .follow-descr {
      transition: all 0.35s ease-in-out;
      -moz-transition: all 0.35s ease-in-out;
      -webkit-transition: all 0.35s ease-in-out;
      -o-transition: all 0.35s ease-in-out;
      -ms-transition: all 0.35s ease-in-out;
    }
    .follow-icon {
      display: table-cell;
      min-width: 40px;
      height: 40px;
      border: 1px solid rgba(255, 255, 255, .3);
      -webkit-background-clip: padding-box; /* for Safari */
      background-clip: padding-box;
      border-right: none;
      vertical-align: middle;
      text-align: center;
      font-size: 16px;
    }
    .follow-descr {
      display: table-cell;
      width: 82%;
      border: 1px solid rgba(255, 255, 255, .3);
      -webkit-background-clip: padding-box; /* for Safari */
      background-clip: padding-box;
      vertical-align: middle;
      padding: 10px;
      .follow-social {
        float: left;
        text-transform: uppercase;
        a {
          text-decoration: none !important;
          color: #999999;
          transition: all 0.35s ease-in-out;
          -moz-transition: all 0.35s ease-in-out;
          -webkit-transition: all 0.35s ease-in-out;
          -o-transition: all 0.35s ease-in-out;
          -ms-transition: all 0.35s ease-in-out;
          &:hover {
            text-decoration: underline !important;
            color: #999999;
          }
        }
      }
      .follow-numb {  float: right;  opacity: .6;  }
    }
  }
}



/* Partners logos */

.partners {  opacity: .4;  margin: 72px 0;  }
#copyrights-wrapper {
  padding: 24px 0 24px 0;
  background-color: $bg-darker;
  .copyright {
    display: inline-block;
    font-size: 12px;
    line-height: 24px;
    color: $fontcolor-medium-light;
    a {  color: $fontcolor-medium-light; &:hover {  color: $fontcolor-medium-light;  } }
  }
  .copy-attrs {  float: left;  margin-right: 30px;  }
  .copy-link {  float: left;  margin-right: 30px;  }
}




/*------------------------------------------------------------------
[13. Media Query]
*/


@media (max-width: 1200px) {

}

@media (min-width: 992px) {

}

@media (max-width: 992px) {

  .overlay-border {display: none;}

  .team-member {  margin-bottom: 32px;  }

  .infoblock {  margin-bottom: 70px;  }

//  Count Block on Mainpage
  .digit {  font-size: 48px;  line-height: 30px;  }
  .counts .count-container .count .count-icon {  font-size: 32px;  }
  .carousel-inner {  margin: 0 auto;  text-align: center;  }



// Header and Menu Mobile Initial look
  #header-wrapper, #header-wrapper-mp {

  }

// Header and Menu Mobile Sticky look

  #header-wrapper .is-sticky,
  #header-wrapper-mp .is-sticky {

  }

// Header and Menu Mobile Initial and Stocky look

  #header-wrapper,
  #header-wrapper-mp,
  #header-wrapper .is-sticky,
  #header-wrapper-mp .is-sticky {
    #site-title {
      float: left;
      h1 {
        font-size: 28px;
        margin-top: 26px;
      }
    }
    .sticky-wrapper, .sticky-header {  height: 80px !important;  }
    .sf-menu {
      .form-inline .form-group {  display: block;  }
      .search-panel {
        input {
          width: 86% !important;
          display: inline-block;
        }
        li {
          right: 0;
          height: 42px;
          padding: 0px 8px;
        }
      }
    }
    #mobnav-btn {
      display: block;
      float: right;
      margin-top: 11px;
    }
    #main-menu {
      float: none;
      background: $bg-dark;
      margin-top: 0;
      .submenu a {
        color: $bg-light;
      }
      ul {  margin-bottom: 0;  }
      a { > i {  color: $bg-light !important;  } &:hover {  color: $fontcolor-invert !important;  } }
      > ul > li > a {  color: $bg-light !important; &:hover{  color: $fontcolor-invert !important;  } }
      > ul > li > .sf-with-ul:after{  border-top-color: $bg-light !important;  }
    }
  }

  .sf-arrows ul .sf-with-ul:after {
    margin-top: -4px;
    margin-right: -5px;
    border-color: transparent;
    border-left-color: $bg-light;
  }
  .mobnav-subarrow {
    display: block;
    opacity: .3;
    height: 20px;
    width: 30px;
    background-position: top left!important;
    position: absolute;
    top: 19px;
    right: 10px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    cursor: pointer;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    cursor: pointer;
  }
  .sf-menu {
    width: 100%!important;
    display: none;
    a {  padding: 1.4em 1.4em;  }
    ul {
      position:static!important;
      display: none!important;
    }
    ul li {
      background: none !important;
      -webkit-box-shadow: none;
      box-shadow: none;
    }
    li {
      float: none!important;
      display: block!important;
      width: 100%!important;
      &:last-child {  border-bottom: none;  }
    }
    li a {  float: none!important;  }
  }
  .sf-menu.xactive {
    display: block!important;
    float: none;
    margin-top:80px;
  }
  .is-sticky .sf-menu.xactive {  margin-top:81px;  }
  .sf-menu li:hover, .sf-menu li.sfHover,
  .sf-menu ul li:hover, .sf-menu ul li.sfHover{
    background: $bg-darker !important;
  }
  .xpopdrop > ul, .xpopdrop > ul > li > ul {
    display: block !important;
    opacity: 1 !important;
  }

  .shop-item {  margin-bottom: 60px;  }

  figure {
    width: 100%;
    max-width: 100%;
    margin: 0 0 0 0;
  }

  .customer-review .review-container .review-content {  text-align: center;  }
  .hp-block {
    .hp-categories-menu {
      text-align: center;
      ul {
        text-align: center;
        padding-left: 0px;
        white-space: normal;
        width: 100%;
        li {width: 100%;}
      }
    }
  }
}

//@media (min-width: 768px) {  }

@media (max-width: 768px) {
  .digit {
    font-size: 38px;
    line-height: 24px;
  }
  .counts .count-container .count .count-icon {  font-size: 24px;  }
  .partners {  display: none;  }
  .port-filter ul li {
	display: block;
	margin-bottom: 12px;
	width: 100%;
	a {width: 100%;  }
  }
}

@media (max-width: 400px) {

  .carousel-inner {  width: 100%;  }
  .overlay-divide {  display: none;  }
  .counts {  padding-bottom: 20px;  }
  .count {
    display: block !important;
    margin-bottom: 30px;
  }
  .sticky-wrapper {  height: 80px;  }
}
