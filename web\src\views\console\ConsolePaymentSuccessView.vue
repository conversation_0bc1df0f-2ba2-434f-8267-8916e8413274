<template>
  <div class="payment-success-container">
    <div class="success-content">
      <div class="success-icon">
        <div class="checkmark">✓</div>
      </div>

      <h1 class="success-title">{{ $t('paymentSuccess') }}</h1>

      <div class="success-message">
        <p>{{ $t('paymentCompleted') }}</p>
      </div>

      <div class="payment-details" v-if="paymentInfo">
        <h3>{{ $t('orderInfo') }}</h3>
        <div class="detail-item">
          <span class="label">{{ $t('orderNumber') }}：</span>
          <span class="value">{{ paymentInfo.paymentId }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('paymentAmount') }}：</span>
          <span class="value">¥{{ paymentInfo.amount }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('selectPaymentMethod') }}：</span>
          <span class="value">{{
            getPaymentMethodText(paymentInfo.paymentMethod)
          }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('generationTime') }}：</span>
          <span class="value">{{ formatDate(paymentInfo.paidAt || '') }}</span>
        </div>
      </div>

      <div class="action-buttons">
        <button @click="goToDashboard" class="btn-primary">{{ $t('goToDownload') }}</button>
        <button @click="viewPaymentHistory" class="btn-secondary">
          {{ $t('myApplications') }}
        </button>
      </div>

      <div class="auto-redirect">
        <p>{{ countdown }}{{ $t('autoRedirectTip') }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { paymentAPI } from "@/services/apiService";
import type { Payment } from "@/types/api";

type PaymentInfo = Payment;

export default defineComponent({
  name: "ConsolePaymentSuccessView",
  setup() {
    const router = useRouter();
    const route = useRoute();
    const { t } = useI18n();

    const paymentInfo = ref<PaymentInfo | null>(null);
    const countdown = ref(10);

    let countdownInterval: number | null = null;

    const goToDashboard = () => {
      // 设置支付成功标志，让仪表板知道用户刚完成支付
      sessionStorage.setItem("paymentSuccess", "true");
      router.push("/console");
    };

    const viewPaymentHistory = () => {
      // 可以创建一个支付历史页面
      router.push("/console"); // 暂时跳转到仪表板
    };

    const getPaymentMethodText = (method: string) => {
      const methodMap: { [key: string]: string } = {
        wechat: t('wechatPay'),
        alipay: t('alipay'),
      };
      return methodMap[method] || method;
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleString("zh-CN");
    };

    const startCountdown = () => {
      countdownInterval = window.setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          goToDashboard();
        }
      }, 1000);
    };

    const stopCountdown = () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
      }
    };

    const loadPaymentInfo = async () => {
      const paymentId = route.query.paymentId as string;
      if (paymentId) {
        try {
          const response = await paymentAPI.getPaymentStatus(paymentId);
          if (
            response.data.success &&
            response.data.payment.status === "paid"
          ) {
            paymentInfo.value = response.data.payment;
          }
        } catch (error) {
          console.error("Failed to load payment info:", error);
        }
      }
    };

    onMounted(() => {
      loadPaymentInfo();
      startCountdown();
    });

    onUnmounted(() => {
      stopCountdown();
    });

    return {
      paymentInfo,
      countdown,
      goToDashboard,
      viewPaymentHistory,
      getPaymentMethodText,
      formatDate,
    };
  },
});
</script>

<style scoped>
.payment-success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: "Arial", sans-serif;
}

.success-content {
  background: white;
  border-radius: 16px;
  padding: 48px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
}

.success-icon {
  margin-bottom: 24px;
}

.checkmark {
  display: inline-block;
  width: 80px;
  height: 80px;
  background-color: #28a745;
  border-radius: 50%;
  color: white;
  font-size: 48px;
  line-height: 80px;
  font-weight: bold;
  animation: checkmarkPulse 0.6s ease-in-out;
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.success-title {
  color: #333;
  font-size: 32px;
  margin: 0 0 16px 0;
  font-weight: bold;
}

.success-message {
  color: #666;
  font-size: 16px;
  margin-bottom: 32px;
  line-height: 1.6;
}

.payment-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.payment-details h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  text-align: center;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 24px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background-color: #545b62;
  transform: translateY(-2px);
}

.auto-redirect {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .success-content {
    padding: 32px 24px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }
}
</style>
